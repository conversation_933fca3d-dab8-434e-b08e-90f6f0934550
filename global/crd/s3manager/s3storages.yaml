---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.17.2
  name: s3storages.s3manager.mtk.zone
spec:
  group: s3manager.mtk.zone
  names:
    kind: S3Storage
    listKind: S3StorageList
    plural: s3storages
    singular: s3storage
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - jsonPath: .spec.provider
      name: Provider
      type: string
    - jsonPath: .spec.region
      name: Region
      type: string
    - jsonPath: .metadata.creationTimestamp
      name: Age
      type: date
    name: v1
    schema:
      openAPIV3Schema:
        description: S3Storage is the Schema for the s3storages API.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: S3StorageSpec defines the desired state of S3Storage.
            properties:
              provider:
                description: Provider specifies which S3 provider to use (e.g., "ovh",
                  "aws", "digitalocean")
                enum:
                - ovh
                type: string
              region:
                description: Region where the S3 repository should be created (e.g.,
                  "eu-west-par", "gra", "sbg", "bhs")
                enum:
                - eu-west-par
                type: string
            required:
            - provider
            - region
            type: object
          status:
            description: S3StorageStatus defines the observed state of S3Storage.
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
