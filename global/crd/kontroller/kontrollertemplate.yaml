apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  name: kontrollertemplates.kontroller.mtk.zone
spec:
  group: kontroller.mtk.zone
  names:
    kind: KontrollerTemplate
    listKind: KontrollerTemplateList
    plural: kontrollertemplates
    singular: kontrollertemplate
  scope: Namespaced
  versions:
    - name: v1
      served: true
      storage: true
      schema:
        openAPIV3Schema:
          properties:
            spec:
              properties:
                limit:
                  description: Limit to this list of instances
                  type: array
                  items:
                    type: string
                create:
                  description: Create condition
                  type: string
                delete:
                  description: Delete condition
                  type: string
                template:
                  description: Resource template
                  type: string
                force:
                  description: Force resource apply
                  type: boolean
                loglevel:
                  description: Activate debug
                  type: string
              required:
                - template
              type: object
          required:
            - spec
          type: object
      additionalPrinterColumns:
        - description: Age
          jsonPath: .metadata.creationTimestamp
          name: Age
          type: date
