# apiVersion: apiextensions.k8s.io/v1
# kind: CustomResourceDefinition
# metadata:
#   name: secmansecrets.secman.mtk.zone
# spec:
#   group: secman.mtk.zone
#   names:
#     kind: SecmanSecret
#     listKind: SecmanSecretsList
#     plural: secmansecrets
#     singular: secmansecret
#   scope: Namespaced
#   versions:
#     - name: v1
#       served: true
#       storage: true
#       schema:
#         openAPIV3Schema:
#           properties:
#             data:
#               description: Contains the secret data to be generated
#               type: object
#               items:
#                 type: string
#               x-kubernetes-preserve-unknown-fields: true
#           required:
#             - data
#           type: object
#       additionalPrinterColumns:
#         - description: Age
#           jsonPath: .metadata.creationTimestamp
#           name: Age
#           type: date
