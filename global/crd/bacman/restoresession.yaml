apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  name: restoresessions.bacman.mtk.zone
spec:
  group: bacman.mtk.zone
  names:
    kind: RestoreSession
    listKind: RestoreSessionList
    plural: restoresessions
    singular: restoresession
  scope: Namespaced
  versions:
    - name: v1
      served: true
      storage: true
      schema:
        openAPIV3Schema:
          properties:
            spec:
              properties:
                snapshot:
                  description: BackupSnapshot
                  type: string
                pvc:
                  description: PersistentVolumeClaim
                  type: string
                node:
                  description: Node
                  type: string
                merge:
                  description: Merge restored data with existing data (default False)
                  type: boolean
                clean:
                  description: Clean up existing files before restore (default False)
                  type: boolean
                test:
                  description: Test restore (default False)
                  type: boolean
              required:
                - snapshot
                - pvc
              type: object
            status:
              properties:
                start_time:
                  description: Start time
                  format: date-time
                  type: string
                end_time:
                  description: End time
                  format: date-time
                  type: string
                elapsed_time:
                  description: Elapsed time
                  format: time
                  type: integer
                state:
                  description: State
                  type: string
                running:
                  description: Running
                  type: boolean
              type: object
              x-kubernetes-preserve-unknown-fields: true
          required:
            - spec
          type: object
      additionalPrinterColumns:
        - description: Age
          jsonPath: .metadata.creationTimestamp
          name: Age
          type: date
        - description: PersistentVolumeClaim
          jsonPath: .spec.pvc
          name: Pvc
          type: string
        - description: State
          jsonPath: .status.state
          name: State
          type: string
        - description: Start
          jsonPath: .status.start_time
          name: Start
          type: date
        - description: Elapsed
          jsonPath: .status.elapsed_time
          name: Elapsed
          type: integer
        - description: Reason
          jsonPath: .status.reason
          name: Reason
          type: string
