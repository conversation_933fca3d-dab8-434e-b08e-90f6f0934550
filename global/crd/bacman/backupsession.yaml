apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  name: backupsessions.bacman.mtk.zone
spec:
  group: bacman.mtk.zone
  names:
    kind: BackupSession
    listKind: BackupSessionList
    plural: backupsessions
    singular: backupsession
  scope: Namespaced
  versions:
    - name: v1
      served: true
      storage: true
      schema:
        openAPIV3Schema:
          properties:
            spec:
              properties:
                pvc:
                  description: PersistentVolumeClaim
                  type: string
                restic_check_subset_percentage:
                  description: Check a randomly choosen subset of the repository pack files
                  type: integer
                  minimum: 0
                  maximum: 100
              required:
                - pvc
              type: object
            status:
              properties:
                start_time:
                  description: Start time
                  format: date-time
                  type: string
                end_time:
                  description: End time
                  format: date-time
                  type: string
                elapsed_time:
                  description: Elapsed time
                  format: time
                  type: integer
                state:
                  description: State
                  type: string
                running:
                  description: Running
                  type: boolean
                snapshot_time:
                  description: Volume snapshot timestamp
                  format: date-time
                  type: string
              type: object
              x-kubernetes-preserve-unknown-fields: true
          required:
            - spec
          type: object
      additionalPrinterColumns:
        - description: Age
          jsonPath: .metadata.creationTimestamp
          name: Age
          type: date
        - description: PersistentVolumeClaim
          jsonPath: .spec.pvc
          name: Pvc
          type: string
        - description: State
          jsonPath: .status.state
          name: State
          type: string
        - description: Start
          jsonPath: .status.start_time
          name: Start
          type: date
        - description: Elapsed
          jsonPath: .status.elapsed_time
          name: Elapsed
          type: integer
        - description: Reason
          jsonPath: .status.reason
          name: Reason
          type: string
