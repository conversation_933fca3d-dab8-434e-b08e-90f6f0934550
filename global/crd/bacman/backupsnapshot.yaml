apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  name: backupsnapshots.bacman.mtk.zone
spec:
  group: bacman.mtk.zone
  names:
    kind: BackupSnapshot
    listKind: BackupSnapshotList
    plural: backupsnapshots
    singular: backupsnapshot
  scope: Namespaced
  versions:
    - name: v1
      served: true
      storage: true
      schema:
        openAPIV3Schema:
          properties:
            spec:
              type: object
              required:
                - pvc
                - id
                - time
              properties:
                pvc:
                  description: PersistentVolumeClaim
                  type: string
                id:
                  description: Id
                  type: string
                time:
                  description: Snapshot timestamp
                  format: date-time
                  type: string
                size:
                  description: Human readable size
                  type: string
                bytes:
                  description: Size in bytes
                  type: integer
                files:
                  description: Number of files
                  type: integer
                volumeClaimTemplate:
                  description: volumeClaimTemplate
                  type: object
                  x-kubernetes-preserve-unknown-fields: true
          required:
            - spec
          type: object
      additionalPrinterColumns:
        - description: Age
          jsonPath: .spec.time
          name: Age
          type: date
        - description: Time
          jsonPath: .spec.time
          name: Time
          type: string
        - description: <PERSON>ze
          jsonPath: .spec.size
          name: Size
          type: string
        - description: Bytes
          jsonPath: .spec.bytes
          name: Bytes
          type: integer
        - description: Files
          jsonPath: .spec.files
          name: Files
          type: integer
