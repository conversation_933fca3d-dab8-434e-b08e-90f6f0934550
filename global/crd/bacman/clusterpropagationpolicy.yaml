apiVersion: policy.karmada.io/v1alpha1
kind: ClusterPropagationPolicy
metadata:
  name: bacman
spec:
  placement:
    clusterAffinity:
      clusterNames: [mc, mg, mt, mv]
  resourceSelectors:
    - apiVersion: apiextensions.k8s.io/v1
      kind: CustomResourceDefinition
      name: backupsessions.bacman.mtk.zone
    - apiVersion: apiextensions.k8s.io/v1
      kind: CustomResourceDefinition
      name: backupsnapshots.bacman.mtk.zone
    - apiVersion: apiextensions.k8s.io/v1
      kind: CustomResourceDefinition
      name: restoresessions.bacman.mtk.zone
