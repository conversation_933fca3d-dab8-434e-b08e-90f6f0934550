#!/bin/bash

# Test script for S3Manager controller
# This script helps verify the controller setup and connectivity

echo "=== S3Manager Controller Test Script ==="
echo

# Check if kubectl is available and configured
echo "1. Checking kubectl connectivity..."
if ! kubectl cluster-info >/dev/null 2>&1; then
    echo "❌ kubectl is not configured or cluster is not accessible"
    echo "   Please ensure kubectl is configured with the correct context"
    exit 1
fi
echo "✅ kubectl connectivity OK"

# Check if the S3Repository CRD is installed
echo
echo "2. Checking S3Repository CRD..."
if ! kubectl get crd s3repositories.s3manager.mtk.zone >/dev/null 2>&1; then
    echo "❌ S3Repository CRD not found"
    echo "   Run: make install"
    exit 1
fi
echo "✅ S3Repository CRD found"

# List existing S3Repository resources
echo
echo "3. Listing existing S3Repository resources..."
kubectl get s3repositories -A
echo

# Check environment variables
echo "4. Checking environment variables..."
if [ -z "$OVH_APPLICATION_KEY" ]; then
    echo "⚠️  OVH_APPLICATION_KEY not set"
else
    echo "✅ OVH_APPLICATION_KEY set"
fi

if [ -z "$OVH_APPLICATION_SECRET" ]; then
    echo "⚠️  OVH_APPLICATION_SECRET not set"
else
    echo "✅ OVH_APPLICATION_SECRET set"
fi

if [ -z "$OVH_CONSUMER_KEY" ]; then
    echo "⚠️  OVH_CONSUMER_KEY not set"
else
    echo "✅ OVH_CONSUMER_KEY set"
fi

if [ -z "$OVH_PROJECT_NAME" ]; then
    echo "⚠️  OVH_PROJECT_NAME not set"
else
    echo "✅ OVH_PROJECT_NAME set to: $OVH_PROJECT_NAME"
fi

if [ -z "$OVH_PROJECT_ID" ]; then
    echo "⚠️  OVH_PROJECT_ID not set"
else
    echo "✅ OVH_PROJECT_ID set to: $OVH_PROJECT_ID"
fi

echo
echo "5. Building and starting controller with debug logging..."
echo "   Press Ctrl+C to stop the controller"
echo

# Build and run the controller
go build cmd/main.go && ./main
