---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: manager-role
rules:
- apiGroups:
  - ""
  resources:
  - secrets
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - s3manager.mtk.zone
  resources:
  - s3repositories
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - s3manager.mtk.zone
  resources:
  - s3repositories/finalizers
  verbs:
  - update
- apiGroups:
  - s3manager.mtk.zone
  resources:
  - s3repositories/status
  verbs:
  - get
  - patch
  - update
