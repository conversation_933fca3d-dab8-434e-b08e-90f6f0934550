---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.17.2
  name: s3repositories.s3manager.mtk.zone
spec:
  group: s3manager.mtk.zone
  names:
    kind: S3Repository
    listKind: S3RepositoryList
    plural: s3repositories
    singular: s3repository
  scope: Namespaced
  versions:
  - name: v1
    schema:
      openAPIV3Schema:
        description: S3Repository is the Schema for the s3repositories API.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: |-
              S3RepositorySpec defines the desired state of S3Repository.
              Repository name is automatically generated from namespace and resource name.
            properties:
              deleteOnRemoval:
                description: Whether the repository should be deleted when the CR
                  is deleted
                type: boolean
              provider:
                description: Provider specifies which S3 provider to use (e.g., "ovh",
                  "aws", "digitalocean")
                enum:
                - ovh
                - aws
                - digitalocean
                type: string
              region:
                description: Region where the S3 repository should be created
                minLength: 1
                type: string
              storageClass:
                description: Storage class for the repository (e.g., "STANDARD", "COLD")
                enum:
                - STANDARD
                - COLD
                type: string
            required:
            - provider
            - region
            type: object
          status:
            description: S3RepositoryStatus defines the observed state of S3Repository.
            properties:
              accessKey:
                description: Access key for S3 access (deprecated - use secret instead)
                type: string
              conditions:
                description: Conditions represent the latest available observations
                  of the S3Repository's state
                items:
                  description: Condition contains details for one aspect of the current
                    state of this API Resource.
                  properties:
                    lastTransitionTime:
                      description: |-
                        lastTransitionTime is the last time the condition transitioned from one status to another.
                        This should be when the underlying condition changed.  If that is not known, then using the time when the API field changed is acceptable.
                      format: date-time
                      type: string
                    message:
                      description: |-
                        message is a human readable message indicating details about the transition.
                        This may be an empty string.
                      maxLength: 32768
                      type: string
                    observedGeneration:
                      description: |-
                        observedGeneration represents the .metadata.generation that the condition was set based upon.
                        For instance, if .metadata.generation is currently 12, but the .status.conditions[x].observedGeneration is 9, the condition is out of date
                        with respect to the current state of the instance.
                      format: int64
                      minimum: 0
                      type: integer
                    reason:
                      description: |-
                        reason contains a programmatic identifier indicating the reason for the condition's last transition.
                        Producers of specific condition types may define expected values and meanings for this field,
                        and whether the values are considered a guaranteed API.
                        The value should be a CamelCase string.
                        This field may not be empty.
                      maxLength: 1024
                      minLength: 1
                      pattern: ^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$
                      type: string
                    status:
                      description: status of the condition, one of True, False, Unknown.
                      enum:
                      - "True"
                      - "False"
                      - Unknown
                      type: string
                    type:
                      description: type of condition in CamelCase or in foo.example.com/CamelCase.
                      maxLength: 316
                      pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                      type: string
                  required:
                  - lastTransitionTime
                  - message
                  - reason
                  - status
                  - type
                  type: object
                type: array
              message:
                description: Human-readable message indicating details about current
                  phase
                type: string
              phase:
                description: Current phase of the S3Repository
                type: string
              repositoryId:
                description: Repository ID assigned by the provider
                type: string
              s3Endpoint:
                description: S3 endpoint URL for accessing the repository
                type: string
              secretName:
                description: Name of the Kubernetes secret containing the S3 user
                  credentials
                type: string
              userId:
                description: User ID of the dedicated S3 user created for this repository
                type: string
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
