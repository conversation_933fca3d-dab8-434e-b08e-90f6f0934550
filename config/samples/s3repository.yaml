apiVersion: s3manager.mtk.zone/v1
kind: S3Repository
metadata:
  name: test
  namespace: default
spec:
  provider: "ovh"  # S3 provider (ovh, aws, digitalocean, etc.)
  # Repository name is automatically generated: mtk-default-test (for OVH)
  region: "EU-WEST-PAR"  # Provider-specific region (e.g., EU-WEST-PAR, gra, sbg, bhs for OVH)
  storageClass: "STANDARD"  # Optional: STANDARD, COLD, etc.
  deleteOnRemoval: true  # Whether to delete the repository when CR is deleted
