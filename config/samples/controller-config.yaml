# S3Manager Controller Configuration Examples
#
# This file shows different ways to configure the S3Manager controller
# with OVH project information and credentials.

---
# Option 1: Configure via environment variables in the deployment
# This is the recommended approach for production deployments
apiVersion: apps/v1
kind: Deployment
metadata:
  name: controller-manager
  namespace: s3manager-system
spec:
  template:
    spec:
      containers:
      - name: manager
        env:
        # OVH Project Configuration
        - name: OVH_PROJECT_NAME
          value: "My S3 Project"  # Replace with your actual project name
        - name: OVH_PROJECT_ID
          value: "1234567890abcdef"  # Replace with your actual project ID
        
        # Global Credentials Configuration (optional)
        - name: OVH_CREDENTIALS_SECRET
          value: "ovh-credentials"
        - name: OVH_CREDENTIALS_NAMESPACE
          value: "s3manager-system"

---
# Option 2: Configure via ConfigMap and environment variables
apiVersion: v1
kind: ConfigMap
metadata:
  name: s3manager-config
  namespace: s3manager-system
data:
  ovh-project-name: "My S3 Project"
  ovh-project-id: "1234567890abcdef"
  ovh-credentials-secret: "ovh-credentials"
  ovh-credentials-namespace: "s3manager-system"

---
# Deployment using ConfigMap
apiVersion: apps/v1
kind: Deployment
metadata:
  name: controller-manager-with-configmap
  namespace: s3manager-system
spec:
  template:
    spec:
      containers:
      - name: manager
        envFrom:
        - configMapRef:
            name: s3manager-config
            # ConfigMap keys will be converted to env vars:
            # ovh-project-name -> OVH_PROJECT_NAME (if you use proper naming)

---
# Option 3: Configure via command line arguments
# Add these args to the container command in the deployment
# args:
#   - --leader-elect
#   - --health-probe-bind-address=:8081
#   - --ovh-project-name=My S3 Project
#   - --ovh-project-id=1234567890abcdef
#   - --ovh-credentials-secret=ovh-credentials
#   - --ovh-credentials-namespace=s3manager-system

---
# Example: Complete deployment with environment variables
apiVersion: apps/v1
kind: Deployment
metadata:
  name: s3manager-controller
  namespace: s3manager-system
  labels:
    control-plane: controller-manager
    app.kubernetes.io/name: s3manager
spec:
  selector:
    matchLabels:
      control-plane: controller-manager
  replicas: 1
  template:
    metadata:
      labels:
        control-plane: controller-manager
    spec:
      securityContext:
        runAsNonRoot: true
      containers:
      - command:
        - /manager
        args:
        - --leader-elect
        - --health-probe-bind-address=:8081
        image: controller:latest
        name: manager
        env:
        # OVH Project Configuration - REQUIRED
        - name: OVH_PROJECT_NAME
          value: "My S3 Project"  # Replace with your OVH project name
        - name: OVH_PROJECT_ID
          value: "1234567890abcdef"  # Replace with your OVH project ID
        
        # Global credentials (optional - can also be specified per S3Repository)
        - name: OVH_CREDENTIALS_SECRET
          value: "ovh-credentials"
        - name: OVH_CREDENTIALS_NAMESPACE
          value: "s3manager-system"
        
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop:
            - "ALL"
        livenessProbe:
          httpGet:
            path: /healthz
            port: 8081
          initialDelaySeconds: 15
          periodSeconds: 20
        readinessProbe:
          httpGet:
            path: /readyz
            port: 8081
          initialDelaySeconds: 5
          periodSeconds: 10
        resources:
          limits:
            cpu: 500m
            memory: 128Mi
          requests:
            cpu: 10m
            memory: 64Mi
      serviceAccountName: controller-manager
      terminationGracePeriodSeconds: 10
