# Example: S3Manager Controller Deployment with OVH Project Configuration
#
# This example shows how to deploy the S3Manager controller with proper
# OVH project configuration using environment variables.

apiVersion: v1
kind: Namespace
metadata:
  name: s3manager-system
  labels:
    app.kubernetes.io/name: s3manager
    app.kubernetes.io/managed-by: kustomize

---
# Secret containing OVH API credentials
apiVersion: v1
kind: Secret
metadata:
  name: ovh-credentials
  namespace: s3manager-system
type: Opaque
stringData:
  # OVH API Credentials
  endpoint: "ovh-eu"  # or ovh-us, ovh-ca, etc.
  applicationKey: "your-application-key"
  applicationSecret: "your-application-secret"
  consumerKey: "your-consumer-key"

  # Project information can also be stored in the secret if preferred
  # projectName: "My S3 Project"
  # projectId: "1234567890abcdef"

---
# Controller deployment with environment variable configuration
apiVersion: apps/v1
kind: Deployment
metadata:
  name: controller-manager
  namespace: s3manager-system
  labels:
    control-plane: controller-manager
    app.kubernetes.io/name: s3manager
    app.kubernetes.io/managed-by: kustomize
spec:
  selector:
    matchLabels:
      control-plane: controller-manager
      app.kubernetes.io/name: s3manager
  replicas: 1
  template:
    metadata:
      annotations:
        kubectl.kubernetes.io/default-container: manager
      labels:
        control-plane: controller-manager
        app.kubernetes.io/name: s3manager
    spec:
      securityContext:
        runAsNonRoot: true
        seccompProfile:
          type: RuntimeDefault
      containers:
      - command:
        - /manager
        args:
        - --leader-elect
        - --health-probe-bind-address=:8081
        image: controller:latest
        name: manager
        env:
        # REQUIRED: OVH Project Configuration
        # Replace these values with your actual OVH project information
        - name: OVH_PROJECT_NAME
          value: "My S3 Project"  # Your OVH project name
        - name: OVH_PROJECT_ID
          value: "1234567890abcdef"  # Your OVH project ID

        # OPTIONAL: Global credentials configuration
        # If not specified, each S3Repository must specify its own credentials
        - name: OVH_CREDENTIALS_SECRET
          value: "ovh-credentials"
        - name: OVH_CREDENTIALS_NAMESPACE
          value: "s3manager-system"

        ports: []
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop:
            - "ALL"
        livenessProbe:
          httpGet:
            path: /healthz
            port: 8081
          initialDelaySeconds: 15
          periodSeconds: 20
        readinessProbe:
          httpGet:
            path: /readyz
            port: 8081
          initialDelaySeconds: 5
          periodSeconds: 10
        resources:
          limits:
            cpu: 500m
            memory: 128Mi
          requests:
            cpu: 10m
            memory: 64Mi
      serviceAccountName: controller-manager
      terminationGracePeriodSeconds: 10

---
# Alternative: Using a ConfigMap for configuration
apiVersion: v1
kind: ConfigMap
metadata:
  name: s3manager-config
  namespace: s3manager-system
data:
  # OVH Project Configuration
  OVH_PROJECT_NAME: "My S3 Project"
  OVH_PROJECT_ID: "1234567890abcdef"
  OVH_CREDENTIALS_SECRET: "ovh-credentials"
  OVH_CREDENTIALS_NAMESPACE: "s3manager-system"

---
# Example deployment using ConfigMap
# (Alternative to the above deployment)
apiVersion: apps/v1
kind: Deployment
metadata:
  name: controller-manager-configmap
  namespace: s3manager-system
  labels:
    control-plane: controller-manager
    app.kubernetes.io/name: s3manager
    app.kubernetes.io/managed-by: kustomize
spec:
  selector:
    matchLabels:
      control-plane: controller-manager-configmap
  replicas: 1
  template:
    metadata:
      labels:
        control-plane: controller-manager-configmap
    spec:
      securityContext:
        runAsNonRoot: true
      containers:
      - command:
        - /manager
        args:
        - --leader-elect
        - --health-probe-bind-address=:8081
        image: controller:latest
        name: manager
        # Load configuration from ConfigMap
        envFrom:
        - configMapRef:
            name: s3manager-config
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop:
            - "ALL"
        resources:
          limits:
            cpu: 500m
            memory: 128Mi
          requests:
            cpu: 10m
            memory: 64Mi
      serviceAccountName: controller-manager
      terminationGracePeriodSeconds: 10

---
# Example S3Repository that uses the global configuration
apiVersion: s3manager.mtk.zone/v1
kind: S3Repository
metadata:
  name: example-repository
  namespace: default
spec:
  provider: "ovh"
  # Repository name is automatically generated: mtk-default-example-repository
  region: "gra"
  storageClass: "STANDARD"
  deleteOnRemoval: true
  # No credentials specified - will use global controller configuration
  credentials:
    secretName: ""  # Empty means use global config

---
# Example S3Repository with its own credentials
apiVersion: s3manager.mtk.zone/v1
kind: S3Repository
metadata:
  name: custom-credentials-repository
  namespace: default
spec:
  provider: "ovh"
  # Repository name is automatically generated: mtk-default-custom-credentials-repository
  region: "sbg"
  storageClass: "COLD"
  deleteOnRemoval: true
  # Use specific credentials for this repository
  credentials:
    secretName: "custom-ovh-credentials"
    namespace: "default"
