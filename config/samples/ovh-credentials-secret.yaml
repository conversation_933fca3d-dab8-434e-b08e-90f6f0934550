apiVersion: v1
kind: Secret
metadata:
  name: ovh-credentials
  namespace: default
  labels:
    app.kubernetes.io/name: s3manager
    app.kubernetes.io/managed-by: kustomize
type: Opaque
stringData:
  # OVH Provider Credentials
  endpoint: "ovh-eu"  # or ovh-us, ovh-ca, etc.
  applicationKey: "your-application-key"
  applicationSecret: "your-application-secret"
  consumerKey: "your-consumer-key"

  # OVH Project Configuration
  # All S3 repositories and users will be created within this project
  projectName: "S3Manager Project"  # Human-readable project name
  projectId: "your-project-id"      # OVH project unique identifier
