# S3Manager Controller Configuration

This document explains how to configure the S3Manager controller with OVH project information and credentials.

## Overview

The S3Manager controller needs to know which OVH project to use for creating S3 repositories and users. Since OVH requires all S3 resources to be created within a project, you must configure the controller with:

- **Project Name**: Human-readable name of your OVH project
- **Project ID**: Unique identifier of your OVH project
- **Credentials**: OVH API credentials (optional global configuration)

## Configuration Methods

### 1. Environment Variables (Recommended)

The most common and Kubernetes-native approach is to use environment variables:

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: controller-manager
  namespace: s3manager-system
spec:
  template:
    spec:
      containers:
      - name: manager
        env:
        - name: OVH_PROJECT_NAME
          value: "My S3 Project"
        - name: OVH_PROJECT_ID
          value: "1234567890abcdef"
        - name: OVH_CREDENTIALS_SECRET
          value: "ovh-credentials"
        - name: OVH_CREDENTIALS_NAMESPACE
          value: "s3manager-system"
```

### 2. Command Line Arguments

You can also pass configuration via command line flags:

```yaml
containers:
- name: manager
  args:
  - --leader-elect
  - --ovh-project-name=My S3 Project
  - --ovh-project-id=1234567890abcdef
  - --ovh-credentials-secret=ovh-credentials
  - --ovh-credentials-namespace=s3manager-system
```

### 3. ConfigMap + Environment Variables

For better configuration management, use a ConfigMap:

```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: s3manager-config
data:
  OVH_PROJECT_NAME: "My S3 Project"
  OVH_PROJECT_ID: "1234567890abcdef"
  OVH_CREDENTIALS_SECRET: "ovh-credentials"
  OVH_CREDENTIALS_NAMESPACE: "s3manager-system"
---
apiVersion: apps/v1
kind: Deployment
spec:
  template:
    spec:
      containers:
      - name: manager
        envFrom:
        - configMapRef:
            name: s3manager-config
```

## Configuration Parameters

| Parameter | Environment Variable | Command Line Flag | Required | Description |
|-----------|---------------------|-------------------|----------|-------------|
| Project Name | `OVH_PROJECT_NAME` | `--ovh-project-name` | Yes | Human-readable name of your OVH project |
| Project ID | `OVH_PROJECT_ID` | `--ovh-project-id` | Yes | Unique identifier of your OVH project |
| Credentials Secret | `OVH_CREDENTIALS_SECRET` | `--ovh-credentials-secret` | No | Name of secret containing OVH API credentials |
| Credentials Namespace | `OVH_CREDENTIALS_NAMESPACE` | `--ovh-credentials-namespace` | No | Namespace of the credentials secret |

## Credential Precedence

The controller supports multiple ways to specify credentials:

1. **Per-Repository**: Each S3Repository can specify its own credentials via `spec.credentials`
2. **Global**: Controller-wide credentials via configuration parameters

**Precedence Order** (highest to lowest):
1. Project settings from controller configuration (always used for project info)
2. API credentials from S3Repository spec
3. API credentials from global controller configuration

## Finding Your OVH Project Information

### Via OVH Control Panel
1. Log in to your OVH Control Panel
2. Go to "Public Cloud" section
3. Select your project
4. The project name and ID are displayed in the project overview

### Via OVH API
```bash
# List all projects
curl -X GET "https://eu.api.ovh.com/1.0/cloud/project" \
  -H "X-Ovh-Application: YOUR_APP_KEY" \
  -H "X-Ovh-Consumer: YOUR_CONSUMER_KEY" \
  -H "X-Ovh-Signature: YOUR_SIGNATURE" \
  -H "X-Ovh-Timestamp: TIMESTAMP"

# Get project details
curl -X GET "https://eu.api.ovh.com/1.0/cloud/project/{projectId}" \
  -H "X-Ovh-Application: YOUR_APP_KEY" \
  -H "X-Ovh-Consumer: YOUR_CONSUMER_KEY" \
  -H "X-Ovh-Signature: YOUR_SIGNATURE" \
  -H "X-Ovh-Timestamp: TIMESTAMP"
```

## Example Configurations

### Production Setup
```yaml
# Use environment variables with proper secrets management
env:
- name: OVH_PROJECT_NAME
  valueFrom:
    secretKeyRef:
      name: ovh-project-config
      key: project-name
- name: OVH_PROJECT_ID
  valueFrom:
    secretKeyRef:
      name: ovh-project-config
      key: project-id
```

### Development Setup
```yaml
# Use direct values for development
env:
- name: OVH_PROJECT_NAME
  value: "Development S3 Project"
- name: OVH_PROJECT_ID
  value: "dev-project-123"
```

## Validation

The controller validates configuration at startup:
- Project name and ID must be non-empty
- If global credentials are specified, the secret must exist and be accessible
- Project information is logged (without sensitive data) for verification

## Troubleshooting

### Common Issues

1. **Missing Project Configuration**
   ```
   Error: OVH project name is required but not configured
   ```
   Solution: Set `OVH_PROJECT_NAME` environment variable or `--ovh-project-name` flag

2. **Invalid Project ID**
   ```
   Error: OVH project ID is required but not configured
   ```
   Solution: Set `OVH_PROJECT_ID` environment variable or `--ovh-project-id` flag

3. **Credentials Secret Not Found**
   ```
   Error: failed to get provider credentials secret
   ```
   Solution: Ensure the credentials secret exists in the specified namespace

### Debugging

Enable debug logging to see configuration details:
```yaml
args:
- --zap-log-level=debug
```

Check controller logs:
```bash
kubectl logs -n s3manager-system deployment/controller-manager
```
