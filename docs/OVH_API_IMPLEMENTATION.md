# OVH API Implementation Guide

This document explains how to implement real OVH API calls using the official OVH Go library.

## Overview

The S3Manager operator uses the official OVH Go library (`github.com/ovh/go-ovh/ovh`) to interact with OVH's S3 storage API. This provides:

- Automatic request signing and authentication
- Structured error handling
- Type-safe API interactions
- Official support and maintenance

## Library Integration

### Dependencies

The OVH library is already added to the project:

```go
import "github.com/ovh/go-ovh/ovh"
```

### Client Creation

The provider creates an OVH client with proper authentication:

```go
client, err := ovh.NewClient(
    endpoint,          // "ovh-eu", "ovh-us", "ovh-ca", etc.
    applicationKey,    // Your application key
    applicationSecret, // Your application secret
    consumerKey,       // Your consumer key
)
```

## API Implementation Examples

### 1. Create S3 Repository

```go
func (p *Provider) CreateRepository(ctx context.Context, req providers.CreateRepositoryRequest) (*providers.Repository, error) {
    // Validate inputs
    if err := p.validateRepositoryName(req.Name); err != nil {
        return nil, err
    }
    if err := p.validateProjectConfig(); err != nil {
        return nil, err
    }

    // Prepare API request
    createReq := CreateS3RepositoryRequest{
        Name:         req.Name,
        Region:       req.Region,
        StorageClass: req.StorageClass,
    }

    // Make API call
    var ovhRepo S3Repository
    endpoint := fmt.Sprintf("/cloud/project/%s/storage/s3/repositories", p.projectID)
    
    if err := p.client.PostWithContext(ctx, endpoint, createReq, &ovhRepo); err != nil {
        return nil, fmt.Errorf("failed to create S3 repository via OVH API: %w", err)
    }

    // Create S3 user for the repository
    user, err := p.createS3User(ctx, ovhRepo.ID)
    if err != nil {
        return nil, fmt.Errorf("failed to create S3 user: %w", err)
    }

    // Get S3 credentials for the user
    credentials, err := p.getS3Credentials(ctx, user.ID)
    if err != nil {
        return nil, fmt.Errorf("failed to get S3 credentials: %w", err)
    }

    // Convert to provider format
    return &providers.Repository{
        ID:           ovhRepo.ID,
        Name:         ovhRepo.Name,
        Region:       ovhRepo.Region,
        StorageClass: ovhRepo.StorageClass,
        Status:       ovhRepo.Status,
        S3Endpoint:   fmt.Sprintf("s3.%s.cloud.ovh.net", ovhRepo.Region),
        AccessKey:    credentials.AccessKey,
        SecretKey:    credentials.SecretKey,
        Provider:     "ovh",
    }, nil
}
```

### 2. Get S3 Repository

```go
func (p *Provider) GetRepository(ctx context.Context, id string) (*providers.Repository, error) {
    var ovhRepo S3Repository
    endpoint := fmt.Sprintf("/cloud/project/%s/storage/s3/repositories/%s", p.projectID, id)
    
    if err := p.client.GetWithContext(ctx, endpoint, &ovhRepo); err != nil {
        return nil, fmt.Errorf("failed to get S3 repository %s: %w", id, err)
    }

    // Get associated S3 user and credentials
    users, err := p.listS3Users(ctx)
    if err != nil {
        return nil, fmt.Errorf("failed to list S3 users: %w", err)
    }

    var accessKey, secretKey string
    for _, user := range users {
        // Find user associated with this repository
        if user.Username == fmt.Sprintf("%s-user", ovhRepo.Name) {
            creds, err := p.getS3Credentials(ctx, user.ID)
            if err == nil {
                accessKey = creds.AccessKey
                secretKey = creds.SecretKey
                break
            }
        }
    }

    return &providers.Repository{
        ID:           ovhRepo.ID,
        Name:         ovhRepo.Name,
        Region:       ovhRepo.Region,
        StorageClass: ovhRepo.StorageClass,
        Status:       ovhRepo.Status,
        S3Endpoint:   fmt.Sprintf("s3.%s.cloud.ovh.net", ovhRepo.Region),
        AccessKey:    accessKey,
        SecretKey:    secretKey,
        Provider:     "ovh",
    }, nil
}
```

### 3. Delete S3 Repository

```go
func (p *Provider) DeleteRepository(ctx context.Context, id string) error {
    // First, clean up associated S3 users
    if err := p.deleteS3UsersForRepository(ctx, id); err != nil {
        return fmt.Errorf("failed to delete S3 users: %w", err)
    }

    // Delete the repository
    endpoint := fmt.Sprintf("/cloud/project/%s/storage/s3/repositories/%s", p.projectID, id)
    
    if err := p.client.DeleteWithContext(ctx, endpoint, nil); err != nil {
        return fmt.Errorf("failed to delete S3 repository %s: %w", id, err)
    }

    return nil
}
```

### 4. Helper Methods

```go
// Create S3 user for repository access
func (p *Provider) createS3User(ctx context.Context, repositoryID string) (*S3User, error) {
    createReq := CreateS3UserRequest{
        Username: fmt.Sprintf("repo-%s-user", repositoryID),
    }

    var user S3User
    endpoint := fmt.Sprintf("/cloud/project/%s/storage/s3/users", p.projectID)
    
    if err := p.client.PostWithContext(ctx, endpoint, createReq, &user); err != nil {
        return nil, err
    }

    return &user, nil
}

// Get S3 credentials for a user
func (p *Provider) getS3Credentials(ctx context.Context, userID string) (*S3Credentials, error) {
    var credentials S3Credentials
    endpoint := fmt.Sprintf("/cloud/project/%s/storage/s3/users/%s/credentials", p.projectID, userID)
    
    if err := p.client.GetWithContext(ctx, endpoint, &credentials); err != nil {
        return nil, err
    }

    return &credentials, nil
}

// List all S3 users in the project
func (p *Provider) listS3Users(ctx context.Context) ([]S3User, error) {
    var users []S3User
    endpoint := fmt.Sprintf("/cloud/project/%s/storage/s3/users", p.projectID)
    
    if err := p.client.GetWithContext(ctx, endpoint, &users); err != nil {
        return nil, err
    }

    return users, nil
}
```

## Error Handling

The OVH library provides structured error handling:

```go
if err := p.client.PostWithContext(ctx, endpoint, request, &response); err != nil {
    // Check for specific OVH API errors
    if ovhErr, ok := err.(*ovh.APIError); ok {
        switch ovhErr.Code {
        case 404:
            return fmt.Errorf("resource not found: %w", err)
        case 409:
            return fmt.Errorf("resource already exists: %w", err)
        case 403:
            return fmt.Errorf("insufficient permissions: %w", err)
        default:
            return fmt.Errorf("OVH API error: %w", err)
        }
    }
    return fmt.Errorf("request failed: %w", err)
}
```

## Testing with Mock Client

For testing, you can create a mock OVH client:

```go
type MockOVHClient struct {
    responses map[string]interface{}
    errors    map[string]error
}

func (m *MockOVHClient) PostWithContext(ctx context.Context, url string, reqBody, resType interface{}) error {
    if err, exists := m.errors[url]; exists {
        return err
    }
    if resp, exists := m.responses[url]; exists {
        // Copy response to resType
        return nil
    }
    return fmt.Errorf("no mock response for %s", url)
}
```

## Next Steps

To implement real OVH API calls:

1. **Remove mock responses** from the provider methods
2. **Uncomment the API calls** in the provider implementation
3. **Add proper error handling** for OVH-specific errors
4. **Implement user management** for S3 access credentials
5. **Add comprehensive logging** for debugging
6. **Test with real OVH account** in a development environment

## API Documentation

- [OVH API Console](https://eu.api.ovh.com/console/)
- [OVH Go Library](https://github.com/ovh/go-ovh)
- [OVH S3 Storage API](https://eu.api.ovh.com/console/#/cloud/project/%7BserviceName%7D/storage/s3)

## Security Considerations

- Store API credentials securely in Kubernetes secrets
- Use least-privilege access for OVH API credentials
- Implement proper request timeouts and retries
- Log API calls for auditing (without sensitive data)
- Validate all inputs before making API calls
