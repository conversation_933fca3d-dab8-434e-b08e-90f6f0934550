# S3Manager Operator

A Kubernetes operator for managing OVH S3 repositories via their HTTP API.

## Description

The S3Manager operator allows you to declaratively manage OVH S3 repositories using Kubernetes Custom Resources. It provides automated creation, monitoring, and deletion of S3 repositories through the OVH API, making it easy to integrate S3 storage provisioning into your Kubernetes workflows.

## Features

- **Declarative Management**: Define S3 repositories using Kubernetes Custom Resources
- **OVH API Integration**: Direct integration with OVH's HTTP API for S3 repository management
- **Credential Management**: Secure credential handling through Kubernetes secrets
- **Status Tracking**: Real-time status updates and condition reporting
- **Finalizer Support**: Proper cleanup with optional repository deletion on CR removal

## Getting Started

### Prerequisites
- go version v1.23.0+
- docker version 17.03+.
- kubectl version v1.11.3+.
- Access to a Kubernetes v1.11.3+ cluster.

### To Deploy on the cluster
**Build and push your image to the location specified by `IMG`:**

```sh
make docker-build docker-push IMG=<some-registry>/s3manager:tag
```

**NOTE:** This image ought to be published in the personal registry you specified.
And it is required to have access to pull the image from the working environment.
Make sure you have the proper permission to the registry if the above commands don’t work.

**Install the CRDs into the cluster:**

```sh
make install
```

**Deploy the Manager to the cluster with the image specified by `IMG`:**

```sh
make deploy IMG=<some-registry>/s3manager:tag
```

> **NOTE**: If you encounter RBAC errors, you may need to grant yourself cluster-admin
privileges or be logged in as admin.

**Configure the Controller:**

The S3Manager controller needs to be configured with your OVH project information. There are several ways to do this:

1. **Environment Variables (Recommended)**:
   ```sh
   # Set these environment variables in the controller deployment
   export OVH_PROJECT_NAME="Your Project Name"
   export OVH_PROJECT_ID="your-project-id"
   ```

2. **Command Line Arguments**:
   ```sh
   # Add these flags to the controller command
   --ovh-project-name="Your Project Name"
   --ovh-project-id="your-project-id"
   ```

3. **Update the deployment configuration**:
   ```sh
   # Edit the manager deployment to include your project info
   kubectl edit deployment controller-manager -n s3manager-system
   ```

**Set up Provider Credentials:**

For OVH provider:
1. Create OVH API credentials at: https://eu.api.ovh.com/createToken/
2. Update the credentials in the sample secret:

```sh
# Edit the secret with your actual provider credentials
kubectl apply -f config/samples/ovh-credentials-secret.yaml
```

> **📖 For detailed configuration options, see [Configuration Guide](docs/CONFIGURATION.md)**
>
> **🔧 For OVH API implementation details, see [OVH API Implementation Guide](docs/OVH_API_IMPLEMENTATION.md)**

**Create instances of your solution:**

```sh
kubectl apply -k config/samples/
```

**Check the status:**

```sh
kubectl get s3repository
kubectl describe s3repository s3repository-sample
```

>**NOTE**: Make sure to update the OVH credentials and project configuration in the secret before applying the samples.

### To Uninstall
**Delete the instances (CRs) from the cluster:**

```sh
kubectl delete -k config/samples/
```

**Delete the APIs(CRDs) from the cluster:**

```sh
make uninstall
```

**UnDeploy the controller from the cluster:**

```sh
make undeploy
```

## API Reference

### S3Repository Spec

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `provider` | string | Yes | S3 provider to use (ovh, aws, digitalocean, etc.) |
| `region` | string | Yes | Provider-specific region where the repository should be created |
| `storageClass` | string | No | Storage class (STANDARD, COLD) |
| `deleteOnRemoval` | bool | No | Whether to delete the repository when CR is deleted |
| `credentials` | object | Yes | Reference to secret containing provider-specific credentials |

**Note**: Repository names are automatically generated from the namespace and resource name:
- **Default format**: `{namespace}-{name}` (e.g., `default-my-repo`)
- **OVH format**: `mtk-{namespace}-{name}` (e.g., `mtk-default-my-repo`)

This ensures consistent naming and avoids conflicts, especially for OVH where the "mtk-" prefix is required.

### S3Repository Status

| Field | Type | Description |
|-------|------|-------------|
| `phase` | string | Current phase (Pending, Ready, Error, Deleting) |
| `message` | string | Human-readable status message |
| `repositoryId` | string | OVH repository ID |
| `s3Endpoint` | string | S3 endpoint URL |
| `accessKey` | string | S3 access key |
| `conditions` | array | Detailed condition information |

### Available Regions

Common OVH regions include:
- `EU-WEST-PAR` - Paris, France (recommended default)
- `gra` - Gravelines, France
- `sbg` - Strasbourg, France
- `bhs` - Beauharnois, Canada
- `de` - Frankfurt, Germany
- `uk` - London, UK

### Storage Classes

- `STANDARD` - Standard storage
- `COLD` - Cold storage (lower cost, higher access latency)

### Provider-Specific Requirements

#### OVH Provider
- **Repository Names**: Automatically generated with `mtk-` prefix to avoid conflicts with other customers
- **Format**: `mtk-{namespace}-{name}` (e.g., `mtk-default-my-repo`, `mtk-production-app-storage`)
- **Validation**: Automatic generation ensures compliance with OVH naming requirements
- **User Creation**: Before creating an OVH object storage repository, a corresponding S3 user must be created with the same name as the repository
  - **User Name**: Matches the repository name (e.g., `mtk-default-my-repo`)
  - **Creation Order**: User is created first, then the repository
- **Project Structure**: All OVH S3 repositories and users are created within a single OVH project
  - **Project Name**: Human-readable name for the OVH project (required in credentials)
  - **Project ID**: Unique identifier for the OVH project (required in credentials)
  - **Single Project**: Each S3Manager controller instance operates within one OVH project
- **API Library**: Uses the official [OVH Go library](https://github.com/ovh/go-ovh) for secure, authenticated API calls

## Project Distribution

Following the options to release and provide this solution to the users.

### By providing a bundle with all YAML files

1. Build the installer for the image built and published in the registry:

```sh
make build-installer IMG=<some-registry>/s3manager:tag
```

**NOTE:** The makefile target mentioned above generates an 'install.yaml'
file in the dist directory. This file contains all the resources built
with Kustomize, which are necessary to install this project without its
dependencies.

2. Using the installer

Users can just run 'kubectl apply -f <URL for YAML BUNDLE>' to install
the project, i.e.:

```sh
kubectl apply -f https://raw.githubusercontent.com/<org>/s3manager/<tag or branch>/dist/install.yaml
```

### By providing a Helm Chart

1. Build the chart using the optional helm plugin

```sh
operator-sdk edit --plugins=helm/v1-alpha
```

2. See that a chart was generated under 'dist/chart', and users
can obtain this solution from there.

**NOTE:** If you change the project, you need to update the Helm Chart
using the same command above to sync the latest changes. Furthermore,
if you create webhooks, you need to use the above command with
the '--force' flag and manually ensure that any custom configuration
previously added to 'dist/chart/values.yaml' or 'dist/chart/manager/manager.yaml'
is manually re-applied afterwards.

## Contributing
// TODO(user): Add detailed information on how you would like others to contribute to this project

**NOTE:** Run `make help` for more information on all potential `make` targets

More information can be found via the [Kubebuilder Documentation](https://book.kubebuilder.io/introduction.html)

## License

Copyright 2025.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

