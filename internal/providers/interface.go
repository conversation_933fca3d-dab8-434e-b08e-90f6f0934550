/*
Copyright 2025.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package providers

import (
	"context"
	"fmt"
)

// S3Provider defines the interface that all S3 providers must implement
type S3Provider interface {
	// GetName returns the provider name (e.g., "ovh", "aws", "digitalocean")
	GetName() string

	// CreateRepository creates a new S3 repository
	CreateRepository(ctx context.Context, req CreateRepositoryRequest) (*Repository, error)

	// GetRepository retrieves an S3 repository by ID
	GetRepository(ctx context.Context, id string) (*Repository, error)

	// DeleteRepository deletes an S3 repository by ID
	DeleteRepository(ctx context.Context, id string) error

	// ListRepositories lists all S3 repositories
	ListRepositories(ctx context.Context) ([]Repository, error)

	// CreateUser creates a new S3 user
	CreateUser(ctx context.Context, req CreateUserRequest) (*User, error)

	// GetUser retrieves an S3 user by ID
	GetUser(ctx context.Context, id string) (*User, error)

	// DeleteUser deletes an S3 user by ID
	DeleteUser(ctx context.Context, id string) error

	// GetUserCredentials retrieves the credentials for an S3 user
	GetUserCredentials(ctx context.Context, userID string) (*UserCredentials, error)

	// ValidateCredentials validates the provider credentials
	ValidateCredentials(ctx context.Context) error
}

// CreateRepositoryRequest represents a request to create an S3 repository
type CreateRepositoryRequest struct {
	Name         string
	Region       string
	StorageClass string
}

// CreateUserRequest represents a request to create an S3 user
type CreateUserRequest struct {
	Username string
}

// Repository represents an S3 repository from any provider
type Repository struct {
	ID           string
	Name         string
	Region       string
	StorageClass string
	Status       string
	S3Endpoint   string
	AccessKey    string
	SecretKey    string
	Provider     string
}

// User represents an S3 user from any provider
type User struct {
	ID       string
	Username string
	Status   string
	Provider string
}

// UserCredentials represents S3 user credentials
type UserCredentials struct {
	AccessKey string
	SecretKey string
}

// ProviderCredentials represents provider-specific credentials
type ProviderCredentials map[string]string

// ProviderFactory creates provider instances
type ProviderFactory interface {
	CreateProvider(providerName string, credentials ProviderCredentials) (S3Provider, error)
	GetSupportedProviders() []string
}

// DefaultProviderFactory implements ProviderFactory
type DefaultProviderFactory struct{}

// CreateProvider creates a provider instance based on the provider name
func (f *DefaultProviderFactory) CreateProvider(providerName string, credentials ProviderCredentials) (S3Provider, error) {
	switch providerName {
	case "ovh":
		return NewOVHProvider(credentials)
	default:
		return nil, &UnsupportedProviderError{Provider: providerName}
	}
}

// GetSupportedProviders returns the list of supported providers
func (f *DefaultProviderFactory) GetSupportedProviders() []string {
	return []string{"ovh"}
}

// UnsupportedProviderError represents an error for unsupported providers
type UnsupportedProviderError struct {
	Provider string
}

func (e *UnsupportedProviderError) Error() string {
	return "unsupported provider: " + e.Provider
}

// NewOVHProvider creates a new OVH provider instance
func NewOVHProvider(credentials ProviderCredentials) (S3Provider, error) {
	// This function is now deprecated - the controller should use the real OVH provider directly
	// to avoid import cycles. Return an error to force the controller to use the real provider.
	return nil, fmt.Errorf("NewOVHProvider is deprecated - use the real OVH provider directly from internal/providers/ovh")
}

// Note: The OVH provider implementation has been moved to internal/providers/ovh/
// to avoid import cycles. The controller should use that package directly.
