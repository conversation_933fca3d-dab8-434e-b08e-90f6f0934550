/*
Copyright 2025.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package ovh

import (
	"context"
	"fmt"
	"os"
	"testing"
	"time"

	"gitlab.mtk.zone/mt-public/s3manager/internal/providers"
)

func TestProvider_validateRepositoryName(t *testing.T) {
	provider := &Provider{
		client:      nil, // Not needed for validation tests
		projectName: "Test Project",
		projectID:   "test-project-id",
	}

	tests := []struct {
		name        string
		repoName    string
		expectError bool
	}{
		{
			name:        "valid name with mtk- prefix",
			repoName:    "mtk-my-repository",
			expectError: false,
		},
		{
			name:        "valid name with mtk- prefix and numbers",
			repoName:    "mtk-app-123",
			expectError: false,
		},
		{
			name:        "invalid name without mtk- prefix",
			repoName:    "my-repository",
			expectError: true,
		},
		{
			name:        "invalid name with wrong prefix",
			repoName:    "other-prefix-repository",
			expectError: true,
		},
		{
			name:        "invalid empty name",
			repoName:    "",
			expectError: true,
		},
		{
			name:        "invalid name starting with mtk but no dash",
			repoName:    "mtkrepository",
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := provider.validateRepositoryName(tt.repoName)
			if tt.expectError && err == nil {
				t.Errorf("expected error for repository name %q, but got none", tt.repoName)
			}
			if !tt.expectError && err != nil {
				t.Errorf("expected no error for repository name %q, but got: %v", tt.repoName, err)
			}
		})
	}
}

func TestProvider_CreateRepository_ValidationError(t *testing.T) {
	provider := &Provider{
		client:      nil, // Not needed for validation tests
		projectName: "Test Project",
		projectID:   "test-project-id",
	}
	ctx := context.Background()

	// Test that CreateRepository fails with invalid name
	req := providers.CreateRepositoryRequest{
		Name:         "invalid-name",
		Region:       "gra",
		StorageClass: "STANDARD",
	}

	_, err := provider.CreateRepository(ctx, req)
	if err == nil {
		t.Error("expected CreateRepository to fail with invalid repository name, but it succeeded")
	}

	expectedError := "OVH S3 repository names must start with 'mtk-'"
	if err != nil && !contains(err.Error(), expectedError) {
		t.Errorf("expected error to contain %q, but got: %v", expectedError, err)
	}
}

func TestProvider_CreateRepository_ValidName(t *testing.T) {
	// This test now requires real credentials since we implemented real API calls
	// Skip this test in unit test mode - it will be covered by integration tests
	t.Skip("Skipping unit test that requires real OVH API calls - covered by integration tests")
}

// Helper function to check if a string contains a substring
func contains(s, substr string) bool {
	return len(s) >= len(substr) && (s == substr || len(substr) == 0 ||
		(len(s) > len(substr) && s[:len(substr)] == substr) ||
		(len(s) > len(substr) && s[len(s)-len(substr):] == substr) ||
		containsSubstring(s, substr))
}

func containsSubstring(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}

func TestProvider_CreateRepository_MissingProjectConfig(t *testing.T) {
	// Test with missing project name
	provider := &Provider{
		client:      nil, // Not needed for validation tests
		projectName: "",
		projectID:   "test-project-id",
	}
	ctx := context.Background()

	req := providers.CreateRepositoryRequest{
		Name:         "mtk-valid-name",
		Region:       "gra",
		StorageClass: "STANDARD",
	}

	_, err := provider.CreateRepository(ctx, req)
	if err == nil {
		t.Error("expected CreateRepository to fail with missing project name, but it succeeded")
	}

	expectedError := "OVH project name is required"
	if err != nil && !contains(err.Error(), expectedError) {
		t.Errorf("expected error to contain %q, but got: %v", expectedError, err)
	}

	// Test with missing project ID
	provider = &Provider{
		client:      nil, // Not needed for validation tests
		projectName: "Test Project",
		projectID:   "",
	}

	_, err = provider.CreateRepository(ctx, req)
	if err == nil {
		t.Error("expected CreateRepository to fail with missing project ID, but it succeeded")
	}

	expectedError = "OVH project ID is required"
	if err != nil && !contains(err.Error(), expectedError) {
		t.Errorf("expected error to contain %q, but got: %v", expectedError, err)
	}
}

func TestProvider_ValidateCredentials(t *testing.T) {
	ctx := context.Background()

	// Test with missing project name
	provider := &Provider{
		client:      nil, // Not needed for validation tests
		projectName: "",
		projectID:   "test-project-id",
	}

	err := provider.ValidateCredentials(ctx)
	if err == nil {
		t.Error("expected ValidateCredentials to fail with missing project name, but it succeeded")
	}

	// Test with missing project ID
	provider = &Provider{
		client:      nil, // Not needed for validation tests
		projectName: "Test Project",
		projectID:   "",
	}

	err = provider.ValidateCredentials(ctx)
	if err == nil {
		t.Error("expected ValidateCredentials to fail with missing project ID, but it succeeded")
	}

	// Note: Testing with valid project configuration now requires real API calls
	// This is covered by integration tests
}

// Integration tests - these require real OVH credentials to be set as environment variables
// Run with: go test -tags=integration ./internal/providers/ovh/

// getTestProvider creates a provider instance for integration testing
// Returns nil if required environment variables are not set
func getTestProvider(t *testing.T) *Provider {
	endpoint := os.Getenv("OVH_ENDPOINT")
	applicationKey := os.Getenv("OVH_APPLICATION_KEY")
	applicationSecret := os.Getenv("OVH_APPLICATION_SECRET")
	consumerKey := os.Getenv("OVH_CONSUMER_KEY")
	projectName := os.Getenv("OVH_PROJECT_NAME")
	projectID := os.Getenv("OVH_PROJECT_ID")

	if endpoint == "" || applicationKey == "" || applicationSecret == "" ||
		consumerKey == "" || projectName == "" || projectID == "" {
		t.Skip("Skipping integration test: OVH credentials not provided via environment variables")
		return nil
	}

	provider, err := NewProvider(endpoint, applicationKey, applicationSecret, consumerKey, projectName, projectID)
	if err != nil {
		t.Fatalf("Failed to create OVH provider: %v", err)
	}

	return provider
}

func TestIntegration_ValidateCredentials(t *testing.T) {
	provider := getTestProvider(t)
	if provider == nil {
		return
	}

	ctx := context.Background()
	err := provider.ValidateCredentials(ctx)
	if err != nil {
		t.Errorf("ValidateCredentials failed with real credentials: %v", err)
	}
}

func TestIntegration_ListRepositories(t *testing.T) {
	provider := getTestProvider(t)
	if provider == nil {
		return
	}

	ctx := context.Background()
	repositories, err := provider.ListRepositories(ctx)
	if err != nil {
		t.Errorf("ListRepositories failed: %v", err)
		return
	}

	t.Logf("Found %d repositories", len(repositories))
	for _, repo := range repositories {
		t.Logf("Repository: ID=%s, Name=%s, Region=%s, Status=%s",
			repo.ID, repo.Name, repo.Region, repo.Status)
	}
}

func TestIntegration_RepositoryLifecycle(t *testing.T) {
	provider := getTestProvider(t)
	if provider == nil {
		return
	}

	ctx := context.Background()

	// Generate a unique repository name for testing
	repoName := fmt.Sprintf("mtk-test-repo-%d", time.Now().Unix())

	// Test CreateRepository
	t.Logf("Creating repository: %s", repoName)
	createReq := providers.CreateRepositoryRequest{
		Name:         repoName,
		Region:       "gra", // Use a common OVH region
		StorageClass: "STANDARD",
	}

	repo, err := provider.CreateRepository(ctx, createReq)
	if err != nil {
		t.Fatalf("CreateRepository failed: %v", err)
	}

	if repo.Name != repoName {
		t.Errorf("Expected repository name %s, got %s", repoName, repo.Name)
	}

	if repo.Provider != "ovh" {
		t.Errorf("Expected provider 'ovh', got %s", repo.Provider)
	}

	t.Logf("Created repository: ID=%s, Name=%s, Status=%s", repo.ID, repo.Name, repo.Status)

	// Test GetRepository
	t.Logf("Getting repository: %s", repo.ID)
	retrievedRepo, err := provider.GetRepository(ctx, repo.ID)
	if err != nil {
		t.Errorf("GetRepository failed: %v", err)
	} else {
		if retrievedRepo.ID != repo.ID {
			t.Errorf("Expected repository ID %s, got %s", repo.ID, retrievedRepo.ID)
		}
		t.Logf("Retrieved repository: ID=%s, Name=%s, Status=%s",
			retrievedRepo.ID, retrievedRepo.Name, retrievedRepo.Status)
	}

	// Wait a bit for the repository to be fully created before cleanup
	time.Sleep(5 * time.Second)

	// Test DeleteRepository
	t.Logf("Deleting repository: %s", repo.ID)
	err = provider.DeleteRepository(ctx, repo.ID)
	if err != nil {
		t.Errorf("DeleteRepository failed: %v", err)
	} else {
		t.Logf("Successfully deleted repository: %s", repo.ID)
	}
}

func TestIntegration_UserLifecycle(t *testing.T) {
	provider := getTestProvider(t)
	if provider == nil {
		return
	}

	ctx := context.Background()

	// Generate a unique username for testing
	username := fmt.Sprintf("test-user-%d", time.Now().Unix())

	// Test CreateUser
	t.Logf("Creating user: %s", username)
	createReq := providers.CreateUserRequest{
		Username: username,
	}

	user, err := provider.CreateUser(ctx, createReq)
	if err != nil {
		t.Fatalf("CreateUser failed: %v", err)
	}

	if user.Username != username {
		t.Errorf("Expected username %s, got %s", username, user.Username)
	}

	if user.Provider != "ovh" {
		t.Errorf("Expected provider 'ovh', got %s", user.Provider)
	}

	t.Logf("Created user: ID=%s, Username=%s, Status=%s", user.ID, user.Username, user.Status)

	// Test GetUser
	t.Logf("Getting user: %s", user.ID)
	retrievedUser, err := provider.GetUser(ctx, user.ID)
	if err != nil {
		t.Errorf("GetUser failed: %v", err)
	} else {
		if retrievedUser.ID != user.ID {
			t.Errorf("Expected user ID %s, got %s", user.ID, retrievedUser.ID)
		}
		t.Logf("Retrieved user: ID=%s, Username=%s, Status=%s",
			retrievedUser.ID, retrievedUser.Username, retrievedUser.Status)
	}

	// Test GetUserCredentials
	t.Logf("Getting credentials for user: %s", user.ID)
	credentials, err := provider.GetUserCredentials(ctx, user.ID)
	if err != nil {
		t.Errorf("GetUserCredentials failed: %v", err)
	} else {
		if credentials.AccessKey == "" {
			t.Error("Expected non-empty access key")
		}
		if credentials.SecretKey == "" {
			t.Error("Expected non-empty secret key")
		}
		t.Logf("Retrieved credentials for user %s: AccessKey=%s...", user.ID, credentials.AccessKey[:8])
	}

	// Wait a bit for the user to be fully created before cleanup
	time.Sleep(5 * time.Second)

	// Test DeleteUser
	t.Logf("Deleting user: %s", user.ID)
	err = provider.DeleteUser(ctx, user.ID)
	if err != nil {
		t.Errorf("DeleteUser failed: %v", err)
	} else {
		t.Logf("Successfully deleted user: %s", user.ID)
	}
}

func TestIntegration_ErrorHandling(t *testing.T) {
	provider := getTestProvider(t)
	if provider == nil {
		return
	}

	ctx := context.Background()

	// Test GetRepository with non-existent ID
	t.Log("Testing GetRepository with non-existent ID")
	_, err := provider.GetRepository(ctx, "non-existent-repo-id")
	if err == nil {
		t.Error("Expected GetRepository to fail with non-existent ID, but it succeeded")
	} else {
		t.Logf("GetRepository correctly failed with error: %v", err)
	}

	// Test GetUser with non-existent ID
	t.Log("Testing GetUser with non-existent ID")
	_, err = provider.GetUser(ctx, "non-existent-user-id")
	if err == nil {
		t.Error("Expected GetUser to fail with non-existent ID, but it succeeded")
	} else {
		t.Logf("GetUser correctly failed with error: %v", err)
	}

	// Test DeleteRepository with non-existent ID
	t.Log("Testing DeleteRepository with non-existent ID")
	err = provider.DeleteRepository(ctx, "non-existent-repo-id")
	if err == nil {
		t.Error("Expected DeleteRepository to fail with non-existent ID, but it succeeded")
	} else {
		t.Logf("DeleteRepository correctly failed with error: %v", err)
	}

	// Test DeleteUser with non-existent ID
	t.Log("Testing DeleteUser with non-existent ID")
	err = provider.DeleteUser(ctx, "non-existent-user-id")
	if err == nil {
		t.Error("Expected DeleteUser to fail with non-existent ID, but it succeeded")
	} else {
		t.Logf("DeleteUser correctly failed with error: %v", err)
	}

	// Test GetUserCredentials with non-existent ID
	t.Log("Testing GetUserCredentials with non-existent ID")
	_, err = provider.GetUserCredentials(ctx, "non-existent-user-id")
	if err == nil {
		t.Error("Expected GetUserCredentials to fail with non-existent ID, but it succeeded")
	} else {
		t.Logf("GetUserCredentials correctly failed with error: %v", err)
	}
}
