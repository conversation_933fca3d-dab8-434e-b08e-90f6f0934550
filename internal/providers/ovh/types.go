/*
Copyright 2025.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package ovh

import "time"

// OVH API types for S3 operations
// These types match the OVH API documentation for S3 storage

// S3Repository represents an OVH S3 repository
// Based on actual OVH API response from POST /cloud/project/{projectId}/region/{region}/storage
type S3Repository struct {
	Name         string    `json:"name"`
	Region       string    `json:"region"`
	CreatedAt    time.Time `json:"createdAt"`
	ObjectsCount int       `json:"objectsCount"`
	ObjectsSize  int64     `json:"objectsSize"`
	OwnerID      int       `json:"ownerId"`
	VirtualHost  string    `json:"virtualHost"`
	// Nested objects from actual API response
	Encryption S3Encryption  `json:"encryption"`
	ObjectLock S3ObjectLock  `json:"objectLock"`
	Versioning S3Versioning  `json:"versioning"`
	Objects    []interface{} `json:"objects"` // Array of objects in the repository
}

// S3Encryption represents the encryption settings for an S3 repository
type S3Encryption struct {
	SSEAlgorithm string `json:"sseAlgorithm"`
}

// S3ObjectLock represents the object lock settings for an S3 repository
type S3ObjectLock struct {
	Status string `json:"status"`
}

// S3Versioning represents the versioning settings for an S3 repository
type S3Versioning struct {
	Status string `json:"status"`
}

// CreateS3RepositoryRequest represents a request to create an S3 repository
// Based on OVH API documentation and actual API testing
type CreateS3RepositoryRequest struct {
	Name    string `json:"containerName"` // Required: container name
	Archive bool   `json:"archive"`       // Required by OVH API - false for standard S3, true for archive storage
	Region  string `json:"region"`        // Required: OVH region for the container
}

// S3User represents an OVH S3 user
type S3User struct {
	ID        int       `json:"id"` // OVH API returns numeric ID
	Username  string    `json:"username"`
	Status    string    `json:"status"`
	CreatedAt time.Time `json:"createdAt"`
	UpdatedAt time.Time `json:"updatedAt"`
}

// CreateS3UserRequest represents a request to create an S3 user
// Based on successful API testing: POST /user with {"description": "mtk-default-test", "role": "objectstore_operator"}
type CreateS3UserRequest struct {
	Description string `json:"description"` // Required: User description (matches repository name)
	Role        string `json:"role"`        // Required: User role for S3 access ("objectstore_operator")
}

// S3Credentials represents S3 access credentials
// Based on actual OVH API response: {"access": "...", "secret": "...", "tenantId": "...", "userId": "..."}
type S3Credentials struct {
	Access   string `json:"access"`   // Maps to AWS_ACCESS_KEY_ID
	Secret   string `json:"secret"`   // Maps to AWS_SECRET_ACCESS_KEY
	TenantID string `json:"tenantId"` // OVH project ID
	UserID   string `json:"userId"`   // OVH user ID
}

// S3Region represents an available S3 region
type S3Region struct {
	Name        string `json:"name"`
	DisplayName string `json:"displayName"`
	Available   bool   `json:"available"`
}

// S3StorageClass represents an available storage class
type S3StorageClass struct {
	Name        string `json:"name"`
	DisplayName string `json:"displayName"`
	Available   bool   `json:"available"`
}

// OVH API endpoints for Object Storage operations
// Based on actual OVH API testing - users are project-level, not S3-specific
const (
	// Object Storage Container endpoints (based on OVH API documentation)
	// Note: Different endpoints may be used for different operations
	S3RepositoriesEndpoint = "/cloud/project/%s/storage"
	S3RepositoryEndpoint   = "/cloud/project/%s/storage/%s"

	// User endpoints (these work - confirmed by testing)
	S3UsersEndpoint = "/cloud/project/%s/user"
	S3UserEndpoint  = "/cloud/project/%s/user/%s"

	// Credentials endpoints (S3-specific credentials)
	S3CredentialsEndpoint = "/cloud/project/%s/user/%s/s3Credentials"

	// Object Storage Configuration endpoints
	S3RegionsEndpoint        = "/cloud/project/%s/storage/regions"
	S3StorageClassesEndpoint = "/cloud/project/%s/storage/storageClasses"
)
