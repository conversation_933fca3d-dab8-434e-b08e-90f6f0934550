/*
Copyright 2025.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package ovh

import (
	"context"
	"fmt"
	"strings"

	"github.com/ovh/go-ovh/ovh"
	"gitlab.mtk.zone/mt-public/s3manager/internal/providers"
	logf "sigs.k8s.io/controller-runtime/pkg/log"
)

// PermanentError represents an error that should not be retried
type PermanentError struct {
	Err error
}

func (e *PermanentError) Error() string {
	return fmt.Sprintf("permanent error: %v", e.Err)
}

func (e *PermanentError) Unwrap() error {
	return e.Err
}

// Provider represents an OVH S3 provider implementation
type Provider struct {
	client *ovh.Client
	// Project configuration - all S3 repositories and users are created within this project
	projectName string
	projectID   string
}

// NewProvider creates a new OVH provider instance
func NewProvider(endpoint, applicationKey, applicationSecret, consumerKey, projectName, projectID string) (*Provider, error) {
	// Create OVH client
	client, err := ovh.NewClient(
		endpoint,
		applicationKey,
		applicationSecret,
		consumerKey,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create OVH client: %w", err)
	}

	return &Provider{
		client:      client,
		projectName: projectName,
		projectID:   projectID,
	}, nil
}

// GetName returns the provider name
func (p *Provider) GetName() string {
	return "ovh"
}

// CreateRepository creates a new S3 repository via OVH API
func (p *Provider) CreateRepository(ctx context.Context, req providers.CreateRepositoryRequest) (*providers.Repository, error) {
	log := logf.FromContext(ctx)

	// Validate OVH-specific naming requirements
	if err := p.validateRepositoryName(req.Name); err != nil {
		return nil, err
	}

	// Validate project configuration
	if err := p.validateProjectConfig(); err != nil {
		return nil, err
	}

	// Prepare the OVH API request
	createReq := CreateS3RepositoryRequest{
		Name:    req.Name,   // Container name
		Archive: false,      // Standard S3 storage, not archive
		Region:  req.Region, // OVH region
	}

	// Make the API call to create the repository
	var ovhRepo S3Repository
	endpoint := fmt.Sprintf(S3RepositoriesEndpoint, p.projectID)

	log.Info("Making OVH API call to create S3 repository",
		"method", "POST",
		"url", endpoint,
		"repositoryName", req.Name)

	// Make the real OVH API call to create the repository
	if err := p.client.PostWithContext(ctx, endpoint, createReq, &ovhRepo); err != nil {
		log.Error(err, "OVH API call failed", "method", "POST", "url", endpoint)
		return nil, p.handleOVHAPIError(err, "create", "S3 repository", req.Name)
	}

	log.Info("OVH API call successful",
		"method", "POST",
		"url", endpoint,
		"repositoryName", ovhRepo.Name,
		"region", ovhRepo.Region,
		"virtualHost", ovhRepo.VirtualHost)

	// Convert OVH repository to provider repository
	// Note: OVH API doesn't return an ID field, so we use the name as the identifier
	return &providers.Repository{
		ID:           ovhRepo.Name, // Use name as ID since OVH API doesn't provide separate ID
		Name:         ovhRepo.Name,
		Region:       ovhRepo.Region,
		StorageClass: "STANDARD",          // Default storage class since not provided by OVH API
		Status:       "active",            // Assume active since repository was created successfully
		S3Endpoint:   ovhRepo.VirtualHost, // Use the virtualHost from OVH API response
		AccessKey:    "",                  // Credentials are retrieved separately via GetUserCredentials
		SecretKey:    "",                  // Credentials are retrieved separately via GetUserCredentials
		Provider:     "ovh",
	}, nil
}

// GetRepository retrieves an S3 repository by ID
func (p *Provider) GetRepository(ctx context.Context, id string) (*providers.Repository, error) {
	log := logf.FromContext(ctx)

	// Validate project configuration
	if err := p.validateProjectConfig(); err != nil {
		return nil, err
	}

	// Prepare the OVH API request
	var ovhRepo S3Repository
	endpoint := fmt.Sprintf(S3RepositoryEndpoint, p.projectID, id)

	log.Info("Making OVH API call to get S3 repository",
		"method", "GET",
		"url", endpoint,
		"repositoryID", id)

	// Make the real OVH API call to get the repository
	if err := p.client.GetWithContext(ctx, endpoint, &ovhRepo); err != nil {
		log.Error(err, "OVH API call failed", "method", "GET", "url", endpoint)
		return nil, p.handleOVHAPIError(err, "get", "S3 repository", id)
	}

	log.Info("OVH API call successful",
		"method", "GET",
		"url", endpoint,
		"repositoryName", ovhRepo.Name,
		"region", ovhRepo.Region,
		"virtualHost", ovhRepo.VirtualHost)

	// Convert OVH repository to provider repository
	return &providers.Repository{
		ID:           ovhRepo.Name, // Use name as ID since OVH API doesn't provide separate ID
		Name:         ovhRepo.Name,
		Region:       ovhRepo.Region,
		StorageClass: "STANDARD",          // Default storage class since not provided by OVH API
		Status:       "active",            // Assume active since repository exists
		S3Endpoint:   ovhRepo.VirtualHost, // Use the virtualHost from OVH API response
		AccessKey:    "",                  // Credentials are retrieved separately via GetUserCredentials
		SecretKey:    "",                  // Credentials are retrieved separately via GetUserCredentials
		Provider:     "ovh",
	}, nil
}

// DeleteRepository deletes an S3 repository by ID
func (p *Provider) DeleteRepository(ctx context.Context, id string) error {
	log := logf.FromContext(ctx)

	// Validate project configuration
	if err := p.validateProjectConfig(); err != nil {
		return err
	}

	// Prepare the OVH API request
	endpoint := fmt.Sprintf(S3RepositoryEndpoint, p.projectID, id)

	log.Info("Making OVH API call to delete S3 repository",
		"method", "DELETE",
		"url", endpoint,
		"repositoryID", id)

	// Make the real OVH API call to delete the repository
	if err := p.client.DeleteWithContext(ctx, endpoint, nil); err != nil {
		log.Error(err, "OVH API call failed", "method", "DELETE", "url", endpoint)
		return p.handleOVHAPIError(err, "delete", "S3 repository", id)
	}

	log.Info("OVH API call successful",
		"method", "DELETE",
		"url", endpoint,
		"repositoryID", id)

	return nil
}

// ListRepositories lists all S3 repositories
func (p *Provider) ListRepositories(ctx context.Context) ([]providers.Repository, error) {
	// Validate project configuration
	if err := p.validateProjectConfig(); err != nil {
		return nil, err
	}

	// Prepare the OVH API request
	var ovhRepos []S3Repository
	endpoint := fmt.Sprintf(S3RepositoriesEndpoint, p.projectID)

	// Make the real OVH API call to list repositories
	if err := p.client.GetWithContext(ctx, endpoint, &ovhRepos); err != nil {
		return nil, p.handleOVHAPIError(err, "list", "S3 repositories", "")
	}

	// Convert OVH repositories to provider repositories
	repositories := make([]providers.Repository, len(ovhRepos))
	for i, ovhRepo := range ovhRepos {
		repositories[i] = providers.Repository{
			ID:           ovhRepo.Name, // Use name as ID since OVH API doesn't provide separate ID
			Name:         ovhRepo.Name,
			Region:       ovhRepo.Region,
			StorageClass: "STANDARD",          // Default storage class since not provided by OVH API
			Status:       "active",            // Assume active since repository exists
			S3Endpoint:   ovhRepo.VirtualHost, // Use the virtualHost from OVH API response
			AccessKey:    "",                  // Not available in list response
			SecretKey:    "",                  // Not available in list response
			Provider:     "ovh",
		}
	}

	return repositories, nil
}

// CreateUser creates a new S3 user via OVH API
func (p *Provider) CreateUser(ctx context.Context, req providers.CreateUserRequest) (*providers.User, error) {
	log := logf.FromContext(ctx)

	// Validate project configuration
	if err := p.validateProjectConfig(); err != nil {
		return nil, err
	}

	// Prepare the OVH API request
	// Based on successful API testing: POST /user with {"description": "mtk-default-test", "role": "objectstore_operator"}
	createReq := CreateS3UserRequest{
		Description: req.Username,           // Use the repository name as user description
		Role:        "objectstore_operator", // Required role for S3 access
	}

	// Make the API call to create the user - using the correct S3 user endpoint
	var ovhUser S3User
	endpoint := fmt.Sprintf(S3UsersEndpoint, p.projectID)

	log.Info("Making OVH API call to create S3 user",
		"method", "POST",
		"url", endpoint,
		"description", createReq.Description,
		"role", createReq.Role)

	// Make the real OVH API call to create the user
	if err := p.client.PostWithContext(ctx, endpoint, createReq, &ovhUser); err != nil {
		log.Error(err, "OVH API call failed", "method", "POST", "url", endpoint)
		return nil, p.handleOVHAPIError(err, "create", "S3 user", req.Username)
	}

	log.Info("OVH API call successful",
		"method", "POST",
		"url", endpoint,
		"userID", ovhUser.ID,
		"status", ovhUser.Status)

	// Convert OVH user to provider user
	return &providers.User{
		ID:       fmt.Sprintf("%d", ovhUser.ID), // Convert int to string
		Username: ovhUser.Username,
		Status:   ovhUser.Status,
		Provider: "ovh",
	}, nil
}

// GetUser retrieves an S3 user by ID
func (p *Provider) GetUser(ctx context.Context, id string) (*providers.User, error) {
	log := logf.FromContext(ctx)

	// Validate project configuration
	if err := p.validateProjectConfig(); err != nil {
		return nil, err
	}

	// Prepare the OVH API request
	var ovhUser S3User
	endpoint := fmt.Sprintf(S3UserEndpoint, p.projectID, id)

	log.Info("Making OVH API call to get S3 user",
		"method", "GET",
		"url", endpoint,
		"userID", id)

	// Make the real OVH API call to get the user
	if err := p.client.GetWithContext(ctx, endpoint, &ovhUser); err != nil {
		log.Error(err, "OVH API call failed", "method", "GET", "url", endpoint)
		return nil, p.handleOVHAPIError(err, "get", "S3 user", id)
	}

	log.Info("OVH API call successful",
		"method", "GET",
		"url", endpoint,
		"userID", ovhUser.ID,
		"status", ovhUser.Status)

	// Convert OVH user to provider user
	return &providers.User{
		ID:       fmt.Sprintf("%d", ovhUser.ID), // Convert int to string
		Username: ovhUser.Username,
		Status:   ovhUser.Status,
		Provider: "ovh",
	}, nil
}

// DeleteUser deletes an S3 user by ID
func (p *Provider) DeleteUser(ctx context.Context, id string) error {
	log := logf.FromContext(ctx)

	// Validate project configuration
	if err := p.validateProjectConfig(); err != nil {
		return err
	}

	// Prepare the OVH API request
	endpoint := fmt.Sprintf(S3UserEndpoint, p.projectID, id)

	log.Info("Making OVH API call to delete S3 user",
		"method", "DELETE",
		"url", endpoint,
		"userID", id)

	// Make the real OVH API call to delete the user
	if err := p.client.DeleteWithContext(ctx, endpoint, nil); err != nil {
		log.Error(err, "OVH API call failed", "method", "DELETE", "url", endpoint)
		return p.handleOVHAPIError(err, "delete", "S3 user", id)
	}

	log.Info("OVH API call successful",
		"method", "DELETE",
		"url", endpoint,
		"userID", id)

	return nil
}

// GetUserCredentials retrieves or creates S3 credentials for a user
func (p *Provider) GetUserCredentials(ctx context.Context, userID string) (*providers.UserCredentials, error) {
	log := logf.FromContext(ctx)

	// Validate project configuration
	if err := p.validateProjectConfig(); err != nil {
		return nil, err
	}

	endpoint := fmt.Sprintf(S3CredentialsEndpoint, p.projectID, userID)

	// Check if credentials already exist - OVH API returns an array of credentials
	var ovhCredentialsList []S3Credentials
	log.Info("Making OVH API call to check existing S3 user credentials",
		"method", "GET",
		"url", endpoint,
		"userID", userID)

	if err := p.client.GetWithContext(ctx, endpoint, &ovhCredentialsList); err != nil {
		// If credentials don't exist (404), create them
		if ovhErr, ok := err.(*ovh.APIError); ok && ovhErr.Code == 404 {
			log.Info("S3 credentials not found, creating new credentials",
				"userID", userID)
			return p.createUserCredentials(ctx, userID)
		}
		log.Error(err, "OVH API call failed", "method", "GET", "url", endpoint)
		return nil, p.handleOVHAPIError(err, "get credentials for", "S3 user", userID)
	}

	// If no credentials exist, create them
	if len(ovhCredentialsList) == 0 {
		log.Info("S3 credentials list is empty, creating new credentials",
			"userID", userID)
		return p.createUserCredentials(ctx, userID)
	}

	// Use the first set of credentials and get the secret
	ovhCredentials := ovhCredentialsList[0]
	log.Info("Found existing S3 credentials, retrieving secret",
		"userID", userID,
		"accessKey", ovhCredentials.Access,
		"credentialsCount", len(ovhCredentialsList))

	// Get the secret for the existing credentials
	secret, err := p.getCredentialSecret(ctx, userID, ovhCredentials.Access)
	if err != nil {
		log.Error(err, "Failed to get credential secret, falling back to creating new credentials")
		return p.createUserCredentials(ctx, userID)
	}

	// Convert OVH credentials to provider credentials
	return &providers.UserCredentials{
		AccessKey: ovhCredentials.Access, // OVH "access" maps to AWS_ACCESS_KEY_ID
		SecretKey: secret,                // Retrieved secret maps to AWS_SECRET_ACCESS_KEY
	}, nil
}

// createUserCredentials creates new S3 credentials for a user
func (p *Provider) createUserCredentials(ctx context.Context, userID string) (*providers.UserCredentials, error) {
	log := logf.FromContext(ctx)

	endpoint := fmt.Sprintf(S3CredentialsEndpoint, p.projectID, userID)

	log.Info("Making OVH API call to create S3 user credentials",
		"method", "POST",
		"url", endpoint,
		"userID", userID)

	// Create S3 credentials - OVH API typically doesn't require a request body for credential creation
	var ovhCredentials S3Credentials
	if err := p.client.PostWithContext(ctx, endpoint, nil, &ovhCredentials); err != nil {
		log.Error(err, "OVH API call failed", "method", "POST", "url", endpoint)
		return nil, p.handleOVHAPIError(err, "create credentials for", "S3 user", userID)
	}

	log.Info("OVH API call successful - S3 credentials created",
		"method", "POST",
		"url", endpoint,
		"hasAccessKey", ovhCredentials.Access != "",
		"hasSecretKey", ovhCredentials.Secret != "")

	// Convert OVH credentials to provider credentials
	return &providers.UserCredentials{
		AccessKey: ovhCredentials.Access, // OVH "access" maps to AWS_ACCESS_KEY_ID
		SecretKey: ovhCredentials.Secret, // OVH "secret" maps to AWS_SECRET_ACCESS_KEY
	}, nil
}

// getCredentialSecret retrieves the secret for existing S3 credentials
func (p *Provider) getCredentialSecret(ctx context.Context, userID, accessKey string) (string, error) {
	log := logf.FromContext(ctx)

	// Build the secret endpoint: /cloud/project/{projectId}/user/{userId}/s3Credentials/{accessKey}/secret
	endpoint := fmt.Sprintf("/cloud/project/%s/user/%s/s3Credentials/%s/secret", p.projectID, userID, accessKey)

	log.Info("Making OVH API call to get S3 credential secret",
		"method", "POST",
		"url", endpoint,
		"userID", userID,
		"accessKey", accessKey)

	// Response structure for the secret endpoint
	var secretResponse struct {
		Secret string `json:"secret"`
	}

	// Make the API call to get/regenerate the secret
	if err := p.client.PostWithContext(ctx, endpoint, nil, &secretResponse); err != nil {
		log.Error(err, "OVH API call failed", "method", "POST", "url", endpoint)
		return "", p.handleOVHAPIError(err, "get secret for", "S3 credentials", accessKey)
	}

	log.Info("OVH API call successful - retrieved credential secret",
		"method", "POST",
		"url", endpoint,
		"hasSecret", secretResponse.Secret != "")

	return secretResponse.Secret, nil
}

// ValidateCredentials validates the OVH API credentials and project configuration
func (p *Provider) ValidateCredentials(ctx context.Context) error {
	log := logf.FromContext(ctx)

	// Validate project configuration
	if err := p.validateProjectConfig(); err != nil {
		return err
	}

	// Validate credentials by attempting to list S3 repositories
	// This is a simple way to verify that the credentials work and the project is accessible
	endpoint := fmt.Sprintf(S3RepositoriesEndpoint, p.projectID)
	var repositories []S3Repository

	log.Info("Making OVH API call to validate credentials",
		"method", "GET",
		"url", endpoint)

	if err := p.client.GetWithContext(ctx, endpoint, &repositories); err != nil {
		log.Error(err, "OVH API call failed during credential validation", "method", "GET", "url", endpoint)
		return p.handleOVHAPIError(err, "validate credentials by listing", "S3 repositories", "")
	}

	log.Info("OVH API call successful - credentials validated",
		"method", "GET",
		"url", endpoint,
		"repositoryCount", len(repositories))

	return nil
}

// validateRepositoryName validates that the repository name follows OVH naming requirements
func (p *Provider) validateRepositoryName(name string) error {
	// OVH S3 repository names must start with "mtk-" to avoid conflicts with other customers
	if !strings.HasPrefix(name, "mtk-") {
		return fmt.Errorf("OVH S3 repository names must start with 'mtk-' to avoid naming conflicts, got: %s", name)
	}

	// Additional OVH naming validations can be added here
	// - Length restrictions
	// - Character restrictions
	// - etc.

	return nil
}

// validateProjectConfig validates that the OVH project configuration is valid
func (p *Provider) validateProjectConfig() error {
	if p.projectName == "" {
		return fmt.Errorf("OVH project name is required but not configured")
	}
	if p.projectID == "" {
		return fmt.Errorf("OVH project ID is required but not configured")
	}
	return nil
}

// isPermanentError checks if an error is permanent and should not be retried
func (p *Provider) isPermanentError(err error) bool {
	if ovhErr, ok := err.(*ovh.APIError); ok {
		switch ovhErr.Code {
		case 400: // Bad Request - usually indicates invalid parameters
			return true
		case 403: // Forbidden - usually indicates insufficient permissions
			return true
		case 404: // Not Found - resource doesn't exist (context dependent)
			return false // 404 can be temporary for newly created resources
		case 405: // Method Not Allowed - API endpoint doesn't support the method
			return true
		case 409: // Conflict - resource already exists or conflicting state
			return true
		case 422: // Unprocessable Entity - validation errors
			return true
		default:
			return false // Treat other errors as potentially temporary
		}
	}
	return false // Non-OVH errors might be temporary
}

// handleOVHAPIError provides enhanced error handling for OVH API errors
func (p *Provider) handleOVHAPIError(err error, operation, resourceType, resourceID string) error {
	if err == nil {
		return nil
	}

	// Create a more descriptive error message
	baseMsg := fmt.Sprintf("OVH API %s operation failed for %s", operation, resourceType)
	if resourceID != "" {
		baseMsg = fmt.Sprintf("OVH API %s operation failed for %s '%s'", operation, resourceType, resourceID)
	}

	// Check if it's an HTTP error and provide more context
	if ovhErr, ok := err.(*ovh.APIError); ok {
		var wrappedErr error
		switch ovhErr.Code {
		case 400:
			wrappedErr = fmt.Errorf("%s: bad request - check parameters: %w", baseMsg, err)
		case 401:
			wrappedErr = fmt.Errorf("%s: authentication failed - check OVH credentials: %w", baseMsg, err)
		case 403:
			wrappedErr = fmt.Errorf("%s: access forbidden - check project permissions: %w", baseMsg, err)
		case 404:
			wrappedErr = fmt.Errorf("%s: resource not found: %w", baseMsg, err)
		case 405:
			wrappedErr = fmt.Errorf("%s: method not allowed - API endpoint doesn't support this operation: %w", baseMsg, err)
		case 409:
			wrappedErr = fmt.Errorf("%s: resource conflict - may already exist: %w", baseMsg, err)
		case 422:
			wrappedErr = fmt.Errorf("%s: unprocessable entity - validation failed: %w", baseMsg, err)
		case 429:
			wrappedErr = fmt.Errorf("%s: rate limit exceeded - please retry later: %w", baseMsg, err)
		case 500, 502, 503, 504:
			wrappedErr = fmt.Errorf("%s: OVH service temporarily unavailable - please retry: %w", baseMsg, err)
		default:
			wrappedErr = fmt.Errorf("%s: HTTP %d: %w", baseMsg, ovhErr.Code, err)
		}

		// Check if this is a permanent error that should not be retried
		if p.isPermanentError(err) {
			return &PermanentError{Err: wrappedErr}
		}
		return wrappedErr
	}

	return fmt.Errorf("%s: %w", baseMsg, err)
}

// convertToOVHRegion converts short region names to OVH API region format
func (p *Provider) convertToOVHRegion(region string) string {
	// Map common OVH region codes to their full API format
	regionMap := map[string]string{
		"gra": "EU-WEST-GRA",    // Gravelines, France
		"sbg": "EU-WEST-SBG",    // Strasbourg, France
		"bhs": "CA-EAST-BHS",    // Beauharnois, Canada
		"waw": "EU-CENTRAL-WAW", // Warsaw, Poland
		"de":  "EU-WEST-DE",     // Frankfurt, Germany
		"uk":  "EU-WEST-UK",     // London, UK
		"par": "EU-WEST-PAR",    // Paris, France
		"rbx": "EU-WEST-RBX",    // Roubaix, France
		"sgp": "ASIA-SGP",       // Singapore
		"syd": "ASIA-SYD",       // Sydney, Australia
	}

	// Return mapped region or uppercase the input if not found
	if ovhRegion, exists := regionMap[region]; exists {
		return ovhRegion
	}

	// Fallback: just uppercase the region if not in our map
	return strings.ToUpper(region)
}
