/*
Copyright 2025.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package controller

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	corev1 "k8s.io/api/core/v1"
	kerrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/types"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/controller/controllerutil"
	logf "sigs.k8s.io/controller-runtime/pkg/log"

	s3managerv1 "gitlab.mtk.zone/mt-public/s3manager/api/v1"
	"gitlab.mtk.zone/mt-public/s3manager/internal/providers"
	"gitlab.mtk.zone/mt-public/s3manager/internal/providers/ovh"
)

const (
	S3RepositoryFinalizer = "s3manager.mtk.zone/finalizer"
	// Maximum number of consecutive failures before giving up
	MaxConsecutiveFailures = 10
	// Annotation to track failure count
	FailureCountAnnotation = "s3manager.mtk.zone/failure-count"
	// Annotation to track last failure time
	LastFailureTimeAnnotation = "s3manager.mtk.zone/last-failure-time"
)

// S3ManagerConfig holds configuration for the S3Manager controller
type S3ManagerConfig struct {
	OVHProjectName          string
	OVHProjectID            string
	OVHCredentialsSecret    string
	OVHCredentialsNamespace string
	// OVH credentials from environment variables (alternative to secret)
	OVHEndpoint          string
	OVHApplicationKey    string
	OVHApplicationSecret string
	OVHConsumerKey       string
}

// S3RepositoryReconciler reconciles a S3Repository object
type S3RepositoryReconciler struct {
	client.Client
	Scheme *runtime.Scheme
	Config S3ManagerConfig
}

// isPermanentError checks if an error is permanent and should not be retried
func (r *S3RepositoryReconciler) isPermanentError(err error) bool {
	// Check if it's wrapped in a PermanentError
	var permErr *ovh.PermanentError
	return errors.As(err, &permErr)
}

// +kubebuilder:rbac:groups=s3manager.mtk.zone,resources=s3repositories,verbs=get;list;watch;create;update;patch;delete
// +kubebuilder:rbac:groups=s3manager.mtk.zone,resources=s3repositories/status,verbs=get;update;patch
// +kubebuilder:rbac:groups=s3manager.mtk.zone,resources=s3repositories/finalizers,verbs=update
// +kubebuilder:rbac:groups="",resources=secrets,verbs=get;list;watch;create;update;patch;delete

// Reconcile is part of the main kubernetes reconciliation loop which aims to
// move the current state of the cluster closer to the desired state.
func (r *S3RepositoryReconciler) Reconcile(ctx context.Context, req ctrl.Request) (ctrl.Result, error) {
	log := logf.FromContext(ctx)

	// Add debug logging to verify controller is receiving events
	log.Info("Reconcile triggered", "namespacedName", req.NamespacedName)

	// Fetch the S3Repository instance
	var s3repo s3managerv1.S3Repository
	if err := r.Get(ctx, req.NamespacedName, &s3repo); err != nil {
		if kerrors.IsNotFound(err) {
			log.Info("S3Repository resource not found. Ignoring since object must be deleted")
			return ctrl.Result{}, nil
		}
		log.Error(err, "Failed to get S3Repository")
		return ctrl.Result{}, err
	}

	// Generate repository name from namespace and resource name
	repositoryName := s3repo.GenerateRepositoryName()

	// Add debug logging to show what we found
	log.Info("Found S3Repository",
		"name", s3repo.Name,
		"namespace", s3repo.Namespace,
		"provider", s3repo.Spec.Provider,
		"repositoryName", repositoryName,
		"currentPhase", s3repo.Status.Phase)

	// Check if the S3Repository instance is marked to be deleted
	if s3repo.ObjectMeta.DeletionTimestamp.IsZero() {
		// The object is not being deleted, so if it does not have our finalizer,
		// then lets add the finalizer and update the object.
		if !controllerutil.ContainsFinalizer(&s3repo, S3RepositoryFinalizer) {
			controllerutil.AddFinalizer(&s3repo, S3RepositoryFinalizer)
			return ctrl.Result{}, r.Update(ctx, &s3repo)
		}
	} else {
		// The object is being deleted
		if controllerutil.ContainsFinalizer(&s3repo, S3RepositoryFinalizer) {
			// Our finalizer is present, so lets handle the external dependency
			if err := r.deleteExternalResources(ctx, &s3repo); err != nil {
				// If fail to delete the external dependency here, return with error
				// so that it can be retried
				return ctrl.Result{}, err
			}

			// Remove our finalizer from the list and update it.
			controllerutil.RemoveFinalizer(&s3repo, S3RepositoryFinalizer)
			return ctrl.Result{}, r.Update(ctx, &s3repo)
		}
		// Stop reconciliation as the item is being deleted
		return ctrl.Result{}, nil
	}

	// Get provider client
	log.Info("Getting provider client",
		"provider", s3repo.Spec.Provider,
		"region", s3repo.Spec.Region,
		"storageClass", s3repo.Spec.StorageClass)
	provider, err := r.getProvider(ctx, &s3repo)
	if err != nil {
		log.Error(err, "Failed to get provider client",
			"provider", s3repo.Spec.Provider)

		// Calculate backoff before updating status
		backoffDuration := r.calculateBackoff(&s3repo)

		r.updateStatus(ctx, &s3repo, s3managerv1.S3RepositoryPhaseError, fmt.Sprintf("Failed to get provider: %v", err))
		log.Info("Scheduling retry with smart backoff", "retryAfter", backoffDuration, "isFirstError", s3repo.Status.Phase != s3managerv1.S3RepositoryPhaseError)
		return ctrl.Result{RequeueAfter: backoffDuration}, nil
	}
	log.Info("Successfully created provider client",
		"provider", s3repo.Spec.Provider)

	// Check if we've exceeded the maximum failure count (circuit breaker)
	if r.hasExceededMaxFailures(&s3repo) {
		log.Error(nil, "Maximum consecutive failures exceeded, stopping reconciliation",
			"maxFailures", MaxConsecutiveFailures,
			"currentFailureCount", r.getFailureCount(&s3repo))
		r.updateStatus(ctx, &s3repo, s3managerv1.S3RepositoryPhaseError,
			fmt.Sprintf("Maximum consecutive failures (%d) exceeded. Manual intervention required.", MaxConsecutiveFailures))
		// Return without requeue to stop the infinite loop
		return ctrl.Result{}, nil
	}

	// Check if we should throttle retries (prevent rapid retries)
	if r.shouldThrottleRetry(&s3repo) {
		log.Info("Throttling retry to prevent rapid failures",
			"failureCount", r.getFailureCount(&s3repo),
			"lastFailureTime", r.getLastFailureTime(&s3repo))
		// Return with a longer requeue time
		return ctrl.Result{RequeueAfter: 10 * time.Minute}, nil
	}

	// Handle the S3Repository based on its current status
	return r.reconcileS3Repository(ctx, &s3repo, provider, repositoryName)
}

// reconcileS3Repository handles the main reconciliation logic
func (r *S3RepositoryReconciler) reconcileS3Repository(ctx context.Context, s3repo *s3managerv1.S3Repository, provider providers.S3Provider, repositoryName string) (ctrl.Result, error) {
	// For OVH provider, user must be created before repository
	// For other providers, repository is created first, then user
	if s3repo.Spec.Provider == "ovh" {
		return r.reconcileOVHResources(ctx, s3repo, provider, repositoryName)
	} else {
		return r.reconcileStandardResources(ctx, s3repo, provider, repositoryName)
	}
}

// reconcileOVHResources handles OVH-specific reconciliation where user must be created before repository
func (r *S3RepositoryReconciler) reconcileOVHResources(ctx context.Context, s3repo *s3managerv1.S3Repository, provider providers.S3Provider, repositoryName string) (ctrl.Result, error) {
	log := logf.FromContext(ctx)

	// Step 1: Create user first (OVH requirement)
	log.Info("Checking user creation status",
		"userID", s3repo.Status.UserID,
		"hasUserID", s3repo.Status.UserID != "",
		"repositoryName", repositoryName)

	if s3repo.Status.UserID == "" {
		log.Info("Creating S3 user first (OVH requirement)", "username", repositoryName)

		req := providers.CreateUserRequest{
			Username: repositoryName, // User name matches repository name
		}

		user, err := provider.CreateUser(ctx, req)
		if err != nil {
			log.Error(err, "Failed to create S3 user")

			// Calculate backoff before updating status (so we can check if this is first error)
			backoffDuration := r.calculateBackoff(s3repo)

			r.updateStatus(ctx, s3repo, s3managerv1.S3RepositoryPhaseError, fmt.Sprintf("Failed to create user: %v", err))
			log.Error(err, "User creation failed, scheduling retry with backoff",
				"retryAfter", backoffDuration,
				"isFirstError", s3repo.Status.Phase != s3managerv1.S3RepositoryPhaseError,
				"errorType", "user_creation")
			return ctrl.Result{RequeueAfter: backoffDuration}, nil
		}

		// Update status with user information
		s3repo.Status.UserID = user.ID
		s3repo.Status.SecretName = s3repo.Name // Secret name matches S3Repository name
		r.updateStatus(ctx, s3repo, s3managerv1.S3RepositoryPhasePending, "User created, waiting for user to be ready")

		return ctrl.Result{RequeueAfter: time.Minute}, nil
	}

	// Step 2: Check user status
	log.Info("Checking S3 user status", "userID", s3repo.Status.UserID)
	user, err := provider.GetUser(ctx, s3repo.Status.UserID)
	if err != nil {
		log.Error(err, "Failed to get S3 user status", "userID", s3repo.Status.UserID)

		// Check if this is a context cancellation error (indicates controller shutdown or timeout)
		if strings.Contains(err.Error(), "context canceled") {
			log.Info("Context was canceled, likely due to controller shutdown. Will retry on next reconciliation.")
			// Don't update status for context cancellation, just return
			return ctrl.Result{RequeueAfter: 5 * time.Minute}, nil
		}

		// Calculate backoff before updating status
		backoffDuration := r.calculateBackoff(s3repo)

		r.updateStatus(ctx, s3repo, s3managerv1.S3RepositoryPhaseError, fmt.Sprintf("Failed to get user status: %v", err))
		log.Error(err, "User status check failed, scheduling retry with backoff",
			"retryAfter", backoffDuration,
			"userID", s3repo.Status.UserID,
			"errorType", "user_status_check")
		return ctrl.Result{RequeueAfter: backoffDuration}, nil
	}
	log.Info("Retrieved S3 user status", "userID", s3repo.Status.UserID, "status", user.Status)

	// Check if user is ready (OVH uses "ok", others might use "ready")
	if user.Status != "ready" && user.Status != "ok" {
		switch user.Status {
		case "creating":
			r.updateStatus(ctx, s3repo, s3managerv1.S3RepositoryPhasePending, "User is being created")
			return ctrl.Result{RequeueAfter: time.Minute}, nil
		case "error":
			r.updateStatus(ctx, s3repo, s3managerv1.S3RepositoryPhaseError, "User creation failed")
			return ctrl.Result{RequeueAfter: time.Minute * 5}, nil
		default:
			r.updateStatus(ctx, s3repo, s3managerv1.S3RepositoryPhasePending, fmt.Sprintf("User status: %s", user.Status))
			return ctrl.Result{RequeueAfter: time.Minute}, nil
		}
	}

	// Step 3: Create repository after user is ready (OVH requirement)
	if s3repo.Status.RepositoryID == "" {
		log.Info("Creating S3 repository after user creation (OVH requirement)", "name", repositoryName)

		req := providers.CreateRepositoryRequest{
			Name:         repositoryName,
			Region:       s3repo.Spec.Region,
			StorageClass: s3repo.Spec.StorageClass,
		}

		repo, err := provider.CreateRepository(ctx, req)
		if err != nil {
			log.Error(err, "Failed to create S3 repository")

			// Check if this is a permanent error that should not be retried
			if r.isPermanentError(err) {
				log.Error(err, "Repository creation failed with permanent error, stopping retries",
					"errorType", "repository_creation_permanent")

				// Check if we're already in Error phase with a permanent error message
				// This prevents infinite reconciliation loops caused by status updates
				currentPhase := s3repo.Status.Phase
				currentMessage := s3repo.Status.Message

				// Check if current message indicates a permanent repository creation error
				// We check for key phrases rather than exact match to avoid OVH Query ID differences
				isPermanentErrorAlreadySet := currentPhase == s3managerv1.S3RepositoryPhaseError &&
					strings.Contains(currentMessage, "Permanent error creating repository") &&
					strings.Contains(currentMessage, "bad request - check parameters")

				log.Info("Checking permanent error status",
					"currentPhase", currentPhase,
					"isPermanentErrorAlreadySet", isPermanentErrorAlreadySet,
					"currentMessageContainsPermanent", strings.Contains(currentMessage, "Permanent error creating repository"))

				if !isPermanentErrorAlreadySet {
					permanentErrorMsg := fmt.Sprintf("Permanent error creating repository: %v", err)
					log.Info("Updating status for permanent error")
					r.updateStatus(ctx, s3repo, s3managerv1.S3RepositoryPhaseError, permanentErrorMsg)
				} else {
					log.Info("Status already reflects permanent error, skipping update")
				}
				// Return without requeue to stop the infinite loop
				return ctrl.Result{}, nil
			}

			// Calculate backoff before updating status
			backoffDuration := r.calculateBackoff(s3repo)

			r.updateStatus(ctx, s3repo, s3managerv1.S3RepositoryPhaseError, fmt.Sprintf("Failed to create repository: %v", err))
			log.Error(err, "Repository creation failed, scheduling retry with backoff",
				"retryAfter", backoffDuration,
				"isFirstError", s3repo.Status.Phase != s3managerv1.S3RepositoryPhaseError,
				"errorType", "repository_creation")
			return ctrl.Result{RequeueAfter: backoffDuration}, nil
		}

		// Update status with repository information
		s3repo.Status.RepositoryID = repo.ID
		s3repo.Status.S3Endpoint = repo.S3Endpoint
		r.updateStatus(ctx, s3repo, s3managerv1.S3RepositoryPhasePending, "Repository created, waiting for it to be ready")

		return ctrl.Result{RequeueAfter: time.Minute}, nil
	}

	// Step 4: Check repository status
	repo, err := provider.GetRepository(ctx, s3repo.Status.RepositoryID)
	if err != nil {
		log.Error(err, "Failed to get S3 repository status")

		// Calculate backoff before updating status
		backoffDuration := r.calculateBackoff(s3repo)

		r.updateStatus(ctx, s3repo, s3managerv1.S3RepositoryPhaseError, fmt.Sprintf("Failed to get repository status: %v", err))
		log.Error(err, "Repository status check failed, scheduling retry with backoff",
			"retryAfter", backoffDuration,
			"repositoryID", s3repo.Status.RepositoryID,
			"errorType", "repository_status_check")
		return ctrl.Result{RequeueAfter: backoffDuration}, nil
	}

	// Handle repository state transitions
	switch repo.Status {
	case "ready":
		// Repository is ready, proceed to credentials
		log.Info("Repository is ready, proceeding to credential handling", "repositoryID", repo.ID)
		return r.handleCredentialsAndSecret(ctx, s3repo, provider, repositoryName)
	case "creating":
		r.updateStatus(ctx, s3repo, s3managerv1.S3RepositoryPhasePending, "Repository is being created")
		return ctrl.Result{RequeueAfter: time.Minute}, nil
	case "error":
		r.updateStatus(ctx, s3repo, s3managerv1.S3RepositoryPhaseError, "Repository creation failed")
		return ctrl.Result{RequeueAfter: time.Minute * 5}, nil
	default:
		r.updateStatus(ctx, s3repo, s3managerv1.S3RepositoryPhasePending, fmt.Sprintf("Repository status: %s", repo.Status))
		return ctrl.Result{RequeueAfter: time.Minute}, nil
	}
}

// reconcileStandardResources handles standard reconciliation where repository is created first, then user
func (r *S3RepositoryReconciler) reconcileStandardResources(ctx context.Context, s3repo *s3managerv1.S3Repository, provider providers.S3Provider, repositoryName string) (ctrl.Result, error) {
	log := logf.FromContext(ctx)

	// If repository ID is not set, create the repository
	if s3repo.Status.RepositoryID == "" {
		log.Info("Creating S3 repository", "name", repositoryName)

		req := providers.CreateRepositoryRequest{
			Name:         repositoryName,
			Region:       s3repo.Spec.Region,
			StorageClass: s3repo.Spec.StorageClass,
		}

		repo, err := provider.CreateRepository(ctx, req)
		if err != nil {
			log.Error(err, "Failed to create S3 repository")

			// Check if this is a permanent error that should not be retried
			if r.isPermanentError(err) {
				log.Error(err, "Repository creation failed with permanent error, stopping retries",
					"errorType", "repository_creation_permanent")

				// Check if we're already in Error phase with a permanent error message
				// This prevents infinite reconciliation loops caused by status updates
				currentPhase := s3repo.Status.Phase
				currentMessage := s3repo.Status.Message

				// Check if current message indicates a permanent repository creation error
				// We check for key phrases rather than exact match to avoid OVH Query ID differences
				isPermanentErrorAlreadySet := currentPhase == s3managerv1.S3RepositoryPhaseError &&
					strings.Contains(currentMessage, "Permanent error creating repository") &&
					strings.Contains(currentMessage, "bad request - check parameters")

				log.Info("Checking permanent error status",
					"currentPhase", currentPhase,
					"isPermanentErrorAlreadySet", isPermanentErrorAlreadySet,
					"currentMessageContainsPermanent", strings.Contains(currentMessage, "Permanent error creating repository"))

				if !isPermanentErrorAlreadySet {
					permanentErrorMsg := fmt.Sprintf("Permanent error creating repository: %v", err)
					log.Info("Updating status for permanent error")
					r.updateStatus(ctx, s3repo, s3managerv1.S3RepositoryPhaseError, permanentErrorMsg)
				} else {
					log.Info("Status already reflects permanent error, skipping update")
				}
				// Return without requeue to stop the infinite loop
				return ctrl.Result{}, nil
			}

			// Calculate backoff before updating status
			backoffDuration := r.calculateBackoff(s3repo)

			r.updateStatus(ctx, s3repo, s3managerv1.S3RepositoryPhaseError, fmt.Sprintf("Failed to create repository: %v", err))
			log.Info("Scheduling retry with smart backoff", "retryAfter", backoffDuration, "isFirstError", s3repo.Status.Phase != s3managerv1.S3RepositoryPhaseError)
			return ctrl.Result{RequeueAfter: backoffDuration}, nil
		}

		// Update status with repository information
		s3repo.Status.RepositoryID = repo.ID
		s3repo.Status.S3Endpoint = repo.S3Endpoint
		s3repo.Status.AccessKey = repo.AccessKey
		r.updateStatus(ctx, s3repo, s3managerv1.S3RepositoryPhasePending, "Repository created, waiting for it to be ready")

		return ctrl.Result{RequeueAfter: time.Minute}, nil
	}

	// Check repository status
	repo, err := provider.GetRepository(ctx, s3repo.Status.RepositoryID)
	if err != nil {
		log.Error(err, "Failed to get S3 repository status")
		r.updateStatus(ctx, s3repo, s3managerv1.S3RepositoryPhaseError, fmt.Sprintf("Failed to get repository status: %v", err))
		return ctrl.Result{RequeueAfter: time.Minute * 5}, nil
	}

	// Handle repository state transitions
	switch repo.Status {
	case "ready":
		// Repository is ready, now handle user creation if not already done
		return r.handleUserCreation(ctx, s3repo, provider, repositoryName)
	case "creating":
		r.updateStatus(ctx, s3repo, s3managerv1.S3RepositoryPhasePending, "Repository is being created")
		return ctrl.Result{RequeueAfter: time.Minute}, nil
	case "error":
		r.updateStatus(ctx, s3repo, s3managerv1.S3RepositoryPhaseError, "Repository creation failed")
		return ctrl.Result{RequeueAfter: time.Minute * 5}, nil
	default:
		r.updateStatus(ctx, s3repo, s3managerv1.S3RepositoryPhasePending, fmt.Sprintf("Repository status: %s", repo.Status))
		return ctrl.Result{RequeueAfter: time.Minute}, nil
	}
}

// handleUserCreation handles the creation of S3 user and associated secret
func (r *S3RepositoryReconciler) handleUserCreation(ctx context.Context, s3repo *s3managerv1.S3Repository, provider providers.S3Provider, repositoryName string) (ctrl.Result, error) {
	log := logf.FromContext(ctx)

	// If user ID is not set, create the user
	if s3repo.Status.UserID == "" {
		log.Info("Creating S3 user", "username", repositoryName)

		req := providers.CreateUserRequest{
			Username: repositoryName, // User name matches repository name
		}

		user, err := provider.CreateUser(ctx, req)
		if err != nil {
			log.Error(err, "Failed to create S3 user")
			r.updateStatus(ctx, s3repo, s3managerv1.S3RepositoryPhaseError, fmt.Sprintf("Failed to create user: %v", err))
			return ctrl.Result{RequeueAfter: time.Minute * 5}, nil
		}

		// Update status with user information
		s3repo.Status.UserID = user.ID
		s3repo.Status.SecretName = s3repo.Name // Secret name matches S3Repository name
		r.updateStatus(ctx, s3repo, s3managerv1.S3RepositoryPhasePending, "User created, waiting for credentials")

		return ctrl.Result{RequeueAfter: time.Minute}, nil
	}

	// Check user status
	user, err := provider.GetUser(ctx, s3repo.Status.UserID)
	if err != nil {
		log.Error(err, "Failed to get S3 user status")
		r.updateStatus(ctx, s3repo, s3managerv1.S3RepositoryPhaseError, fmt.Sprintf("Failed to get user status: %v", err))
		return ctrl.Result{RequeueAfter: time.Minute * 5}, nil
	}

	// Handle user state transitions
	switch user.Status {
	case "ready", "ok": // OVH uses "ok", others might use "ready"
		// User is ready, get credentials and create secret
		return r.handleCredentialsAndSecret(ctx, s3repo, provider, repositoryName)
	case "creating":
		r.updateStatus(ctx, s3repo, s3managerv1.S3RepositoryPhasePending, "User is being created")
		return ctrl.Result{RequeueAfter: time.Minute}, nil
	case "error":
		r.updateStatus(ctx, s3repo, s3managerv1.S3RepositoryPhaseError, "User creation failed")
		return ctrl.Result{RequeueAfter: time.Minute * 5}, nil
	default:
		r.updateStatus(ctx, s3repo, s3managerv1.S3RepositoryPhasePending, fmt.Sprintf("User status: %s", user.Status))
		return ctrl.Result{RequeueAfter: time.Minute}, nil
	}
}

// handleCredentialsAndSecret handles getting user credentials and creating/updating the secret
func (r *S3RepositoryReconciler) handleCredentialsAndSecret(ctx context.Context, s3repo *s3managerv1.S3Repository, provider providers.S3Provider, repositoryName string) (ctrl.Result, error) {
	log := logf.FromContext(ctx)

	// Get user credentials
	credentials, err := provider.GetUserCredentials(ctx, s3repo.Status.UserID)
	if err != nil {
		log.Error(err, "Failed to get user credentials")
		r.updateStatus(ctx, s3repo, s3managerv1.S3RepositoryPhaseError, fmt.Sprintf("Failed to get user credentials: %v", err))
		return ctrl.Result{RequeueAfter: time.Minute * 5}, nil
	}

	// Create or update the secret with credentials
	if err := r.createOrUpdateSecret(ctx, s3repo, credentials, repositoryName); err != nil {
		log.Error(err, "Failed to create/update secret")
		r.updateStatus(ctx, s3repo, s3managerv1.S3RepositoryPhaseError, fmt.Sprintf("Failed to manage secret: %v", err))
		return ctrl.Result{RequeueAfter: time.Minute * 5}, nil
	}

	// Everything is ready
	r.updateStatus(ctx, s3repo, s3managerv1.S3RepositoryPhaseReady, "Repository and user are ready, credentials stored in secret")
	// Don't requeue when everything is working - let Kubernetes handle updates via watches
	return ctrl.Result{}, nil
}

// getProvider creates a provider client using credentials from the referenced secret or environment variables
func (r *S3RepositoryReconciler) getProvider(ctx context.Context, s3repo *s3managerv1.S3Repository) (providers.S3Provider, error) {
	log := logf.FromContext(ctx)

	// Check if we have environment variable credentials for OVH
	if s3repo.Spec.Provider == "ovh" && r.hasOVHEnvCredentials() {
		log.Info("Using OVH credentials from environment variables",
			"endpoint", r.Config.OVHEndpoint,
			"projectName", r.Config.OVHProjectName,
			"projectID", r.Config.OVHProjectID,
			"hasApplicationKey", r.Config.OVHApplicationKey != "",
			"hasApplicationSecret", r.Config.OVHApplicationSecret != "",
			"hasConsumerKey", r.Config.OVHConsumerKey != "")
		return r.createProviderFromEnvCredentials(s3repo)
	}

	log.Info("Environment credentials not available, trying secrets",
		"hasEnvCredentials", r.hasOVHEnvCredentials(),
		"provider", s3repo.Spec.Provider,
		"configuredSecret", r.Config.OVHCredentialsSecret,
		"configuredNamespace", r.Config.OVHCredentialsNamespace)

	// Use global credentials from controller config
	var secretName, secretNamespace string
	if r.Config.OVHCredentialsSecret != "" {
		secretName = r.Config.OVHCredentialsSecret
		secretNamespace = r.Config.OVHCredentialsNamespace
	} else {
		return nil, fmt.Errorf("no credentials configured: neither controller config nor environment variables specify credentials")
	}

	var secret corev1.Secret
	secretKey := types.NamespacedName{
		Name:      secretName,
		Namespace: secretNamespace,
	}

	log.Info("Attempting to get credentials from secret",
		"secretName", secretName,
		"secretNamespace", secretNamespace)

	if err := r.Get(ctx, secretKey, &secret); err != nil {
		log.Error(err, "Failed to get provider credentials secret",
			"secretName", secretName,
			"secretNamespace", secretNamespace)
		return nil, fmt.Errorf("failed to get provider credentials secret %s/%s: %w", secretNamespace, secretName, err)
	}

	log.Info("Successfully retrieved credentials secret",
		"secretName", secretName,
		"secretNamespace", secretNamespace,
		"dataKeys", func() []string {
			keys := make([]string, 0, len(secret.Data))
			for k := range secret.Data {
				keys = append(keys, k)
			}
			return keys
		}())

	// Convert secret data to provider credentials
	credentials := make(providers.ProviderCredentials)
	for key, value := range secret.Data {
		credentials[key] = string(value)
	}

	// Override with controller config if available (controller config takes precedence for project settings)
	if s3repo.Spec.Provider == "ovh" {
		if r.Config.OVHProjectName != "" {
			credentials["projectName"] = r.Config.OVHProjectName
		}
		if r.Config.OVHProjectID != "" {
			credentials["projectId"] = r.Config.OVHProjectID
		}
	}

	// Create provider directly for OVH, or use factory for others
	log.Info("Creating provider",
		"provider", s3repo.Spec.Provider,
		"hasCredentials", len(credentials) > 0)

	if s3repo.Spec.Provider == "ovh" {
		// Use the real OVH provider directly
		provider, err := ovh.NewProvider(
			credentials["endpoint"],
			credentials["applicationKey"],
			credentials["applicationSecret"],
			credentials["consumerKey"],
			credentials["projectName"],
			credentials["projectId"],
		)
		if err != nil {
			log.Error(err, "Failed to create OVH provider", "provider", s3repo.Spec.Provider)
			return nil, fmt.Errorf("failed to create OVH provider: %w", err)
		}
		log.Info("Successfully created OVH provider", "provider", s3repo.Spec.Provider)
		return provider, nil
	}

	// For other providers, use the factory (fallback)
	factory := &providers.DefaultProviderFactory{}
	provider, err := factory.CreateProvider(s3repo.Spec.Provider, credentials)
	if err != nil {
		log.Error(err, "Failed to create provider", "provider", s3repo.Spec.Provider)
		return nil, fmt.Errorf("failed to create provider %s: %w", s3repo.Spec.Provider, err)
	}

	log.Info("Successfully created provider", "provider", s3repo.Spec.Provider)
	return provider, nil
}

// hasOVHEnvCredentials checks if OVH credentials are available in environment variables
func (r *S3RepositoryReconciler) hasOVHEnvCredentials() bool {
	return r.Config.OVHApplicationKey != "" &&
		r.Config.OVHApplicationSecret != "" &&
		r.Config.OVHConsumerKey != "" &&
		r.Config.OVHEndpoint != ""
}

// createProviderFromEnvCredentials creates a provider using environment variable credentials
func (r *S3RepositoryReconciler) createProviderFromEnvCredentials(s3repo *s3managerv1.S3Repository) (providers.S3Provider, error) {
	// For OVH provider, use the real implementation directly
	if s3repo.Spec.Provider == "ovh" {
		return ovh.NewProvider(
			r.Config.OVHEndpoint,
			r.Config.OVHApplicationKey,
			r.Config.OVHApplicationSecret,
			r.Config.OVHConsumerKey,
			r.Config.OVHProjectName,
			r.Config.OVHProjectID,
		)
	}

	// For other providers, use the factory (fallback)
	credentials := make(providers.ProviderCredentials)
	credentials["endpoint"] = r.Config.OVHEndpoint
	credentials["applicationKey"] = r.Config.OVHApplicationKey
	credentials["applicationSecret"] = r.Config.OVHApplicationSecret
	credentials["consumerKey"] = r.Config.OVHConsumerKey

	// Add project configuration
	if r.Config.OVHProjectName != "" {
		credentials["projectName"] = r.Config.OVHProjectName
	}
	if r.Config.OVHProjectID != "" {
		credentials["projectId"] = r.Config.OVHProjectID
	}

	// Create provider using factory
	factory := &providers.DefaultProviderFactory{}
	provider, err := factory.CreateProvider(s3repo.Spec.Provider, credentials)
	if err != nil {
		return nil, fmt.Errorf("failed to create provider %s from environment credentials: %w", s3repo.Spec.Provider, err)
	}

	return provider, nil
}

// deleteExternalResources deletes the external resources associated with the S3Repository
func (r *S3RepositoryReconciler) deleteExternalResources(ctx context.Context, s3repo *s3managerv1.S3Repository) error {
	log := logf.FromContext(ctx)

	if !s3repo.Spec.DeleteOnRemoval {
		log.Info("DeleteOnRemoval is false, skipping external resource deletion")
		// Still delete the secret even if we don't delete external resources
		if err := r.deleteSecret(ctx, s3repo); err != nil {
			log.Error(err, "Failed to delete secret during cleanup")
			// Don't return error here as it's not critical for finalizer removal
		}
		return nil
	}

	provider, err := r.getProvider(ctx, s3repo)
	if err != nil {
		return fmt.Errorf("failed to get provider for deletion: %w", err)
	}

	// For OVH, delete repository first, then user (reverse of creation order)
	// For other providers, delete user first, then repository
	if s3repo.Spec.Provider == "ovh" {
		// Delete S3 repository first for OVH
		if s3repo.Status.RepositoryID != "" {
			log.Info("Deleting S3 repository (OVH)", "repositoryID", s3repo.Status.RepositoryID)
			if err := provider.DeleteRepository(ctx, s3repo.Status.RepositoryID); err != nil {
				log.Error(err, "Failed to delete S3 repository", "repositoryID", s3repo.Status.RepositoryID)
				// Continue with user deletion even if repository deletion fails
			}
		}

		// Delete S3 user after repository for OVH
		if s3repo.Status.UserID != "" {
			log.Info("Deleting S3 user (OVH)", "userID", s3repo.Status.UserID)
			if err := provider.DeleteUser(ctx, s3repo.Status.UserID); err != nil {
				return fmt.Errorf("failed to delete S3 user: %w", err)
			}
		}
	} else {
		// Delete S3 user first for other providers
		if s3repo.Status.UserID != "" {
			log.Info("Deleting S3 user", "userID", s3repo.Status.UserID)
			if err := provider.DeleteUser(ctx, s3repo.Status.UserID); err != nil {
				log.Error(err, "Failed to delete S3 user", "userID", s3repo.Status.UserID)
				// Continue with repository deletion even if user deletion fails
			}
		}

		// Delete S3 repository after user for other providers
		if s3repo.Status.RepositoryID != "" {
			log.Info("Deleting S3 repository", "repositoryID", s3repo.Status.RepositoryID)
			if err := provider.DeleteRepository(ctx, s3repo.Status.RepositoryID); err != nil {
				return fmt.Errorf("failed to delete S3 repository: %w", err)
			}
		}
	}

	// Delete the Kubernetes secret
	if err := r.deleteSecret(ctx, s3repo); err != nil {
		log.Error(err, "Failed to delete secret during cleanup")
		// Don't return error here as the secret will be deleted by owner reference anyway
	}

	return nil
}

// createOrUpdateSecret creates or updates a Kubernetes secret with S3 user credentials
func (r *S3RepositoryReconciler) createOrUpdateSecret(ctx context.Context, s3repo *s3managerv1.S3Repository, credentials *providers.UserCredentials, repositoryName string) error {
	log := logf.FromContext(ctx)

	// Create secret object
	// Build the full S3 endpoint URL with bucket name
	s3EndpointURL := fmt.Sprintf("%s/%s", s3repo.Status.S3Endpoint, repositoryName)

	secret := &corev1.Secret{
		ObjectMeta: metav1.ObjectMeta{
			Name:      s3repo.Name, // Secret name matches S3Repository name
			Namespace: s3repo.Namespace,
		},
		Type: corev1.SecretTypeOpaque,
		Data: map[string][]byte{
			"AWS_ACCESS_KEY_ID":     []byte(credentials.AccessKey),
			"AWS_SECRET_ACCESS_KEY": []byte(credentials.SecretKey),
			"AWS_S3_ENDPOINT_URL":   []byte(s3EndpointURL),
			"AWS_S3_REGION_NAME":    []byte(s3repo.Spec.Region),
		},
	}

	// Set owner reference so the secret is deleted when S3Repository is deleted
	if err := controllerutil.SetControllerReference(s3repo, secret, r.Scheme); err != nil {
		return fmt.Errorf("failed to set controller reference on secret: %w", err)
	}

	// Try to get existing secret
	var existingSecret corev1.Secret
	secretKey := types.NamespacedName{
		Name:      secret.Name,
		Namespace: secret.Namespace,
	}

	if err := r.Get(ctx, secretKey, &existingSecret); err != nil {
		if kerrors.IsNotFound(err) {
			// Secret doesn't exist, create it
			log.Info("Creating secret for S3Repository", "secret", secret.Name)
			if err := r.Create(ctx, secret); err != nil {
				return fmt.Errorf("failed to create secret: %w", err)
			}
		} else {
			return fmt.Errorf("failed to get existing secret: %w", err)
		}
	} else {
		// Secret exists, update it
		log.Info("Updating existing secret for S3Repository", "secret", secret.Name)
		existingSecret.Data = secret.Data
		if err := r.Update(ctx, &existingSecret); err != nil {
			return fmt.Errorf("failed to update secret: %w", err)
		}
	}

	return nil
}

// deleteSecret deletes the Kubernetes secret associated with the S3Repository
func (r *S3RepositoryReconciler) deleteSecret(ctx context.Context, s3repo *s3managerv1.S3Repository) error {
	log := logf.FromContext(ctx)

	if s3repo.Status.SecretName == "" {
		log.Info("No secret name found, nothing to delete")
		return nil
	}

	secret := &corev1.Secret{
		ObjectMeta: metav1.ObjectMeta{
			Name:      s3repo.Status.SecretName,
			Namespace: s3repo.Namespace,
		},
	}

	if err := r.Delete(ctx, secret); err != nil {
		if kerrors.IsNotFound(err) {
			log.Info("Secret already deleted", "secret", s3repo.Status.SecretName)
			return nil
		}
		return fmt.Errorf("failed to delete secret %s: %w", s3repo.Status.SecretName, err)
	}

	log.Info("Successfully deleted secret", "secret", s3repo.Status.SecretName)
	return nil
}

// calculateBackoff calculates exponential backoff duration based on failure count
func (r *S3RepositoryReconciler) calculateBackoff(s3repo *s3managerv1.S3Repository) time.Duration {
	// Smart backoff: quick first retry, then exponential backoff for subsequent failures

	// Check if this is the first error (transitioning from non-error to error state)
	// If the resource was not in error state before, give it a quick retry
	if s3repo.Status.Phase != s3managerv1.S3RepositoryPhaseError {
		// First error - quick retry in 30 seconds
		return 30 * time.Second
	}

	// For subsequent errors, use progressive backoff based on error type
	errorMessage := s3repo.Status.Message

	// Handle context cancellation and timeout errors with longer backoff
	if strings.Contains(errorMessage, "context canceled") || strings.Contains(errorMessage, "context deadline exceeded") {
		// Context errors: longer backoff to avoid rapid retries
		return 5 * time.Minute
	}

	if strings.Contains(errorMessage, "OVH API") {
		// API errors: start with 2 minutes, then increase
		return 2 * time.Minute
	}

	if strings.Contains(errorMessage, "Failed to create") {
		// Creation failures: start with 1 minute, then increase
		return 1 * time.Minute
	}

	if strings.Contains(errorMessage, "Failed to get") {
		// Retrieval failures: moderate retry
		return 2 * time.Minute
	}

	// Default: 90 seconds for other errors
	return 90 * time.Second
}

// isRetryableError determines if an error should trigger a retry or be considered permanent
func (r *S3RepositoryReconciler) isRetryableError(err error) bool {
	if err == nil {
		return false
	}

	errorStr := err.Error()

	// Context cancellation errors are retryable but need longer backoff
	if strings.Contains(errorStr, "context canceled") || strings.Contains(errorStr, "context deadline exceeded") {
		return true
	}

	// Network errors are retryable
	if strings.Contains(errorStr, "connection refused") || strings.Contains(errorStr, "timeout") {
		return true
	}

	// OVH API temporary errors are retryable
	if strings.Contains(errorStr, "rate limit") || strings.Contains(errorStr, "temporarily unavailable") {
		return true
	}

	// 5xx HTTP errors are retryable
	if strings.Contains(errorStr, "HTTP 5") {
		return true
	}

	// 404 errors for resources that should exist are retryable (might be eventual consistency)
	if strings.Contains(errorStr, "404") && (strings.Contains(errorStr, "user") || strings.Contains(errorStr, "repository")) {
		return true
	}

	// Authentication errors are not retryable
	if strings.Contains(errorStr, "401") || strings.Contains(errorStr, "403") {
		return false
	}

	// Bad request errors are not retryable
	if strings.Contains(errorStr, "400") {
		return false
	}

	// Default: assume retryable
	return true
}

// updateStatus updates the status of the S3Repository
func (r *S3RepositoryReconciler) updateStatus(ctx context.Context, s3repo *s3managerv1.S3Repository, phase s3managerv1.S3RepositoryPhase, message string) {
	s3repo.Status.Phase = phase
	s3repo.Status.Message = message

	// Update failure tracking annotations
	if phase == s3managerv1.S3RepositoryPhaseError {
		r.incrementFailureCount(s3repo)
	} else if phase == s3managerv1.S3RepositoryPhaseReady {
		r.resetFailureCount(s3repo)
	}

	// Update conditions
	condition := metav1.Condition{
		Type:               string(phase),
		Status:             metav1.ConditionTrue,
		LastTransitionTime: metav1.Now(),
		Reason:             string(phase),
		Message:            message,
	}

	// Find and update existing condition or add new one
	found := false
	for i, cond := range s3repo.Status.Conditions {
		if cond.Type == condition.Type {
			s3repo.Status.Conditions[i] = condition
			found = true
			break
		}
	}
	if !found {
		s3repo.Status.Conditions = append(s3repo.Status.Conditions, condition)
	}

	if err := r.Status().Update(ctx, s3repo); err != nil {
		logf.FromContext(ctx).Error(err, "Failed to update S3Repository status")
	}
}

// hasExceededMaxFailures checks if the resource has exceeded the maximum consecutive failures
func (r *S3RepositoryReconciler) hasExceededMaxFailures(s3repo *s3managerv1.S3Repository) bool {
	return r.getFailureCount(s3repo) >= MaxConsecutiveFailures
}

// HasExceededMaxFailures is a public wrapper for testing
func (r *S3RepositoryReconciler) HasExceededMaxFailures(s3repo *s3managerv1.S3Repository) bool {
	return r.hasExceededMaxFailures(s3repo)
}

// getFailureCount gets the current failure count from annotations
func (r *S3RepositoryReconciler) getFailureCount(s3repo *s3managerv1.S3Repository) int {
	if s3repo.Annotations == nil {
		return 0
	}

	countStr, exists := s3repo.Annotations[FailureCountAnnotation]
	if !exists {
		return 0
	}

	count := 0
	if _, err := fmt.Sscanf(countStr, "%d", &count); err != nil {
		return 0
	}

	return count
}

// GetFailureCount is a public wrapper for testing
func (r *S3RepositoryReconciler) GetFailureCount(s3repo *s3managerv1.S3Repository) int {
	return r.getFailureCount(s3repo)
}

// incrementFailureCount increments the failure count annotation
func (r *S3RepositoryReconciler) incrementFailureCount(s3repo *s3managerv1.S3Repository) {
	if s3repo.Annotations == nil {
		s3repo.Annotations = make(map[string]string)
	}

	currentCount := r.getFailureCount(s3repo)
	s3repo.Annotations[FailureCountAnnotation] = fmt.Sprintf("%d", currentCount+1)
	s3repo.Annotations[LastFailureTimeAnnotation] = time.Now().Format(time.RFC3339)
}

// IncrementFailureCount is a public wrapper for testing
func (r *S3RepositoryReconciler) IncrementFailureCount(s3repo *s3managerv1.S3Repository) {
	r.incrementFailureCount(s3repo)
}

// resetFailureCount resets the failure count annotation
func (r *S3RepositoryReconciler) resetFailureCount(s3repo *s3managerv1.S3Repository) {
	if s3repo.Annotations == nil {
		return
	}

	delete(s3repo.Annotations, FailureCountAnnotation)
	delete(s3repo.Annotations, LastFailureTimeAnnotation)
}

// ResetFailureCount is a public wrapper for testing
func (r *S3RepositoryReconciler) ResetFailureCount(s3repo *s3managerv1.S3Repository) {
	r.resetFailureCount(s3repo)
}

// shouldThrottleRetry checks if we should throttle retries based on failure count and timing
func (r *S3RepositoryReconciler) shouldThrottleRetry(s3repo *s3managerv1.S3Repository) bool {
	failureCount := r.getFailureCount(s3repo)

	// No throttling for first few failures
	if failureCount < 3 {
		return false
	}

	lastFailureTime := r.getLastFailureTime(s3repo)
	if lastFailureTime.IsZero() {
		return false
	}

	// Throttle if last failure was recent and we have multiple failures
	timeSinceLastFailure := time.Since(lastFailureTime)
	minTimeBetweenRetries := time.Duration(failureCount) * time.Minute

	return timeSinceLastFailure < minTimeBetweenRetries
}

// ShouldThrottleRetry is a public wrapper for testing
func (r *S3RepositoryReconciler) ShouldThrottleRetry(s3repo *s3managerv1.S3Repository) bool {
	return r.shouldThrottleRetry(s3repo)
}

// getLastFailureTime gets the last failure time from annotations
func (r *S3RepositoryReconciler) getLastFailureTime(s3repo *s3managerv1.S3Repository) time.Time {
	if s3repo.Annotations == nil {
		return time.Time{}
	}

	timeStr, exists := s3repo.Annotations[LastFailureTimeAnnotation]
	if !exists {
		return time.Time{}
	}

	t, err := time.Parse(time.RFC3339, timeStr)
	if err != nil {
		return time.Time{}
	}

	return t
}

// SetupWithManager sets up the controller with the Manager.
func (r *S3RepositoryReconciler) SetupWithManager(mgr ctrl.Manager) error {
	return ctrl.NewControllerManagedBy(mgr).
		For(&s3managerv1.S3Repository{}).
		Named("s3repository").
		Complete(r)
}
