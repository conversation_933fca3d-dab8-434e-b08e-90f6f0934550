# S3Manager Operator - Project Guidelines & Directions

This document outlines the strategic direction, architectural decisions, and development priorities for the S3Manager Operator project.

## Project Vision & Goals

### Primary Objective
Create a robust Kubernetes operator that manages S3 repositories from multiple cloud providers through their APIs, providing declarative infrastructure management for S3 storage.

### Key Principles
- **Provider Extensibility**: Design for multiple S3 providers (OVH first, others later)
- **Kubernetes Native**: Follow Kubernetes patterns and conventions
- **Production Ready**: Build for reliability, observability, and maintainability
- **Step-by-Step Development**: Implement features incrementally with proper testing

## Architectural Decisions

### Domain & Naming
- **Domain**: `mtk.zone` (base domain for all operators)
- **Group**: `s3manager` (specific group for S3 management)
- **Resource**: `S3Repository` (represents OVH S3 repository instances)

### Package Structure
```
internal/
├── providers/    # Provider implementations
│   ├── ovh/      # OVH provider implementation
│   └── interface.go # Provider interface definition
└── controller/   # Kubernetes controller logic
api/v1/           # CRD definitions
config/           # Kubernetes manifests
```

### Technology Stack
- **Base**: Kubebuilder/operator-sdk framework
- **Language**: Go 1.23+
- **Providers**: Multiple S3 providers (OVH first, extensible to others)
- **Authentication**: Provider-specific credentials via Kubernetes secrets

## Development Priorities

### Phase 1: Core Functionality ✅ COMPLETE
- [x] Basic operator scaffold with correct domain/group
- [x] S3Repository CRD with comprehensive spec/status
- [x] Controller with finalizer support and proper reconciliation
- [x] OVH client package (placeholder implementation)
- [x] Sample resources and documentation

### Phase 2: Provider Architecture & OVH Implementation ✅ COMPLETE
- [x] Create provider interface for S3 operations
- [x] Refactor existing OVH code to implement provider interface
- [x] Add provider field to S3Repository CRD
- [x] Update controller to use provider abstraction
- [x] Add OVH project configuration support
- [x] Implement controller configuration via environment variables and command line flags
- [x] Add comprehensive validation for project configuration
- [ ] Implement real OVH S3 API calls (NEXT)
- [ ] Add proper OVH API authentication and signing
- [ ] Test with real OVH account (non-production)

### Phase 3: Additional Providers & Enhanced Features
- [ ] Add support for second S3 provider (AWS, DigitalOcean, etc.)
- [ ] Secret management for S3 access keys returned by API
- [ ] Webhook validation for S3Repository resources
- [ ] Provider-specific feature support
- [ ] Metrics and monitoring integration
- [ ] Enhanced status reporting and events

### Phase 4: Production Readiness
- [ ] Comprehensive testing (unit, integration, e2e)
- [ ] Security review and hardening
- [ ] Performance optimization
- [ ] Documentation and examples
- [ ] CI/CD pipeline setup

## Technical Guidelines for AI Development

### When Adding New Features
1. **Research First**: Always research OVH API documentation before implementing
2. **Incremental Changes**: Make small, testable changes that can be committed separately
3. **Maintain Patterns**: Follow existing code patterns and structure
4. **Update Documentation**: Always update README and samples when adding features

### Provider Integration Approach
- Define a common provider interface for S3 operations
- Keep provider-specific logic in `internal/providers/{provider}/` packages
- Use proper HTTP clients with timeouts and retries
- Implement provider-specific authentication according to their documentation
- Handle rate limiting and API quotas appropriately
- Mock provider responses for testing
- Support provider selection via S3Repository spec
- Implement provider-specific validation (e.g., OVH naming requirements)

### Error Handling Strategy
- Use structured logging with context
- Implement exponential backoff for retries
- Distinguish between permanent and temporary errors
- Update S3Repository status with meaningful error messages
- Use Kubernetes events for important state changes

### Validation Strategy
- **CRD-Level Validation**: Use kubebuilder markers and CEL expressions for immediate feedback
- **Provider-Level Validation**: Implement additional validation in provider code for complex rules
- **Dual Validation**: Critical requirements (like OVH naming) enforced at both levels
- **User Experience**: CRD validation provides immediate feedback, provider validation catches edge cases

### Status Management
- Use phases: Pending, Ready, Error, Deleting
- Maintain detailed conditions array
- Include relevant provider repository information in status
- Update status atomically to avoid race conditions

### Provider-Specific Requirements

#### OVH Provider
- **Repository Naming**: All repository names must start with "mtk-" to avoid conflicts with other customers
- **Validation**: Enforced at CRD level (CEL validation) and provider level (Go validation)
- **Regions**: Use OVH-specific region codes (EU-WEST-PAR is preferred default, gra, sbg, bhs, de, uk, etc.)
- **Authentication**: Requires endpoint, applicationKey, applicationSecret, and consumerKey
- **Project Structure**: OVH repositories and users must be created inside a project
  - **Project Name**: Human-readable name for the OVH project
  - **Project ID**: Unique identifier for the OVH project
  - **Single Project**: Each S3Manager controller instance operates within a single OVH project
- **API**: Uses OVH's proprietary API, not standard S3 API

## Future Considerations

### Potential Extensions
- Support for multiple provider accounts/projects
- Backup and restore functionality
- Integration with provider-specific services
- Multi-region repository management
- Cross-provider repository migration
- Provider-specific optimization features

### Integration Points
- Prometheus metrics for monitoring
- Integration with GitOps workflows
- Support for Helm charts
- Integration with backup solutions

## Development Workflow

### For New Features
1. Update this document with the planned approach
2. Research OVH API requirements
3. Implement in small, testable increments
4. Update tests and documentation
5. Commit each logical step separately

### For Bug Fixes
1. Reproduce the issue
2. Add test case that demonstrates the bug
3. Fix the issue
4. Verify the test passes
5. Update documentation if needed

## Important Notes for AI Assistants

### Always Remember
- This operator manages S3 repositories from multiple providers (OVH first)
- Use the domain `mtk.zone` with group `s3manager` consistently
- Design for provider extensibility from the start
- The user prefers step-by-step development with commits between steps
- Mock implementations should be clearly marked and replaced with real API calls
- Follow Kubernetes operator best practices

### Never Do Without Permission
- Change the domain or group names
- Remove existing functionality
- Make breaking changes to the API
- Deploy to production environments
- Commit or push code automatically

### When Uncertain
- Ask for clarification on OVH API specifics
- Propose the approach before implementing
- Suggest testing strategies
- Recommend documentation updates

## Resources

- [OVH API Documentation](https://eu.api.ovh.com/)
- [Kubebuilder Book](https://book.kubebuilder.io/)
- [Kubernetes API Conventions](https://github.com/kubernetes/community/blob/master/contributors/devel/sig-architecture/api-conventions.md)
- [Operator Best Practices](https://sdk.operatorframework.io/docs/best-practices/)

---

**Last Updated**: 2025-06-23
**Next Review**: When starting Phase 2 (OVH API Integration)
