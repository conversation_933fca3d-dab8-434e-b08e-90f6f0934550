/*
Copyright 2025.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package v1

import (
	"fmt"
	"strings"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// EDIT THIS FILE!  THIS IS SCAFFOLDING FOR YOU TO OWN!
// NOTE: json tags are required.  Any new fields you add must have json tags for the fields to be serialized.

// S3RepositorySpec defines the desired state of S3Repository.
// Repository name is automatically generated from namespace and resource name.
type S3RepositorySpec struct {
	// Provider specifies which S3 provider to use (e.g., "ovh", "aws", "digitalocean")
	// +kubebuilder:validation:Enum=ovh;aws;digitalocean
	Provider string `json:"provider"`

	// Region where the S3 repository should be created
	// +kubebuilder:validation:MinLength=1
	Region string `json:"region"`

	// Storage class for the repository (e.g., "STANDARD", "COLD")
	// +kubebuilder:validation:Enum=STANDARD;COLD
	// +optional
	StorageClass string `json:"storageClass,omitempty"`

	// Whether the repository should be deleted when the CR is deleted
	// +optional
	DeleteOnRemoval bool `json:"deleteOnRemoval,omitempty"`
}

// S3RepositoryStatus defines the observed state of S3Repository.
type S3RepositoryStatus struct {
	// Current phase of the S3Repository
	// +optional
	Phase S3RepositoryPhase `json:"phase,omitempty"`

	// Human-readable message indicating details about current phase
	// +optional
	Message string `json:"message,omitempty"`

	// Repository ID assigned by the provider
	// +optional
	RepositoryID string `json:"repositoryId,omitempty"`

	// S3 endpoint URL for accessing the repository
	// +optional
	S3Endpoint string `json:"s3Endpoint,omitempty"`

	// Access key for S3 access (deprecated - use secret instead)
	// +optional
	AccessKey string `json:"accessKey,omitempty"`

	// User ID of the dedicated S3 user created for this repository
	// +optional
	UserID string `json:"userId,omitempty"`

	// Name of the Kubernetes secret containing the S3 user credentials
	// +optional
	SecretName string `json:"secretName,omitempty"`

	// Conditions represent the latest available observations of the S3Repository's state
	// +optional
	Conditions []metav1.Condition `json:"conditions,omitempty"`
}

// S3RepositoryPhase represents the current phase of S3Repository
type S3RepositoryPhase string

const (
	// S3RepositoryPhasePending indicates the repository is being created
	S3RepositoryPhasePending S3RepositoryPhase = "Pending"
	// S3RepositoryPhaseReady indicates the repository is ready for use
	S3RepositoryPhaseReady S3RepositoryPhase = "Ready"
	// S3RepositoryPhaseError indicates an error occurred
	S3RepositoryPhaseError S3RepositoryPhase = "Error"
	// S3RepositoryPhaseDeleting indicates the repository is being deleted
	S3RepositoryPhaseDeleting S3RepositoryPhase = "Deleting"
)

// +kubebuilder:object:root=true
// +kubebuilder:subresource:status

// S3Repository is the Schema for the s3repositories API.
type S3Repository struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`

	Spec   S3RepositorySpec   `json:"spec,omitempty"`
	Status S3RepositoryStatus `json:"status,omitempty"`
}

// +kubebuilder:object:root=true

// S3RepositoryList contains a list of S3Repository.
type S3RepositoryList struct {
	metav1.TypeMeta `json:",inline"`
	metav1.ListMeta `json:"metadata,omitempty"`
	Items           []S3Repository `json:"items"`
}

func init() {
	SchemeBuilder.Register(&S3Repository{}, &S3RepositoryList{})
}

// GenerateRepositoryName generates a repository name from namespace and resource name
// with provider-specific prefixes. For OVH, it adds "mtk-" prefix.
// Format: [prefix-]namespace-name
func (s *S3Repository) GenerateRepositoryName() string {
	baseName := fmt.Sprintf("%s-%s", s.Namespace, s.Name)

	// Sanitize the name to ensure it meets repository naming requirements
	baseName = strings.ToLower(baseName)
	baseName = strings.ReplaceAll(baseName, "_", "-")

	// Add provider-specific prefix
	switch s.Spec.Provider {
	case "ovh":
		return fmt.Sprintf("mtk-%s", baseName)
	default:
		return baseName
	}
}
