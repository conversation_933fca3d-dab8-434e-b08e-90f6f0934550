/*
Copyright 2025.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package v1

import (
	"testing"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

func TestS3Repository_GenerateRepositoryName(t *testing.T) {
	tests := []struct {
		name     string
		s3repo   S3Repository
		expected string
	}{
		{
			name: "OVH provider with simple names",
			s3repo: S3Repository{
				ObjectMeta: metav1.ObjectMeta{
					Name:      "test",
					Namespace: "default",
				},
				Spec: S3RepositorySpec{
					Provider: "ovh",
				},
			},
			expected: "mtk-default-test",
		},
		{
			name: "OVH provider with complex names",
			s3repo: S3Repository{
				ObjectMeta: metav1.ObjectMeta{
					Name:      "my-app-repo",
					Namespace: "production",
				},
				Spec: S3RepositorySpec{
					Provider: "ovh",
				},
			},
			expected: "mtk-production-my-app-repo",
		},
		{
			name: "AWS provider with simple names",
			s3repo: S3Repository{
				ObjectMeta: metav1.ObjectMeta{
					Name:      "test",
					Namespace: "default",
				},
				Spec: S3RepositorySpec{
					Provider: "aws",
				},
			},
			expected: "default-test",
		},
		{
			name: "DigitalOcean provider with simple names",
			s3repo: S3Repository{
				ObjectMeta: metav1.ObjectMeta{
					Name:      "backup-repo",
					Namespace: "staging",
				},
				Spec: S3RepositorySpec{
					Provider: "digitalocean",
				},
			},
			expected: "staging-backup-repo",
		},
		{
			name: "Name with underscores gets sanitized",
			s3repo: S3Repository{
				ObjectMeta: metav1.ObjectMeta{
					Name:      "test_repo",
					Namespace: "my_namespace",
				},
				Spec: S3RepositorySpec{
					Provider: "ovh",
				},
			},
			expected: "mtk-my-namespace-test-repo",
		},
		{
			name: "Mixed case gets lowercased",
			s3repo: S3Repository{
				ObjectMeta: metav1.ObjectMeta{
					Name:      "TestRepo",
					Namespace: "MyNamespace",
				},
				Spec: S3RepositorySpec{
					Provider: "ovh",
				},
			},
			expected: "mtk-mynamespace-testrepo",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.s3repo.GenerateRepositoryName()
			if result != tt.expected {
				t.Errorf("GenerateRepositoryName() = %v, want %v", result, tt.expected)
			}
		})
	}
}
