#!/bin/bash

# OVH S3 Provider Integration Test Runner
# This script runs integration tests for the OVH S3 provider with real OVH credentials

set -e

echo "OVH S3 Provider Integration Test Runner"
echo "======================================="

# Check if required environment variables are set
required_vars=(
    "OVH_ENDPOINT"
    "OVH_APPLICATION_KEY"
    "OVH_APPLICATION_SECRET"
    "OVH_CONSUMER_KEY"
    "OVH_PROJECT_NAME"
    "OVH_PROJECT_ID"
)

missing_vars=()
for var in "${required_vars[@]}"; do
    if [[ -z "${!var}" ]]; then
        missing_vars+=("$var")
    fi
done

if [[ ${#missing_vars[@]} -gt 0 ]]; then
    echo "ERROR: Missing required environment variables:"
    for var in "${missing_vars[@]}"; do
        echo "  - $var"
    done
    echo ""
    echo "Please set these environment variables before running the integration tests."
    echo ""
    echo "Example:"
    echo "export OVH_ENDPOINT='ovh-eu'"
    echo "export OVH_APPLICATION_KEY='your-application-key'"
    echo "export OVH_APPLICATION_SECRET='your-application-secret'"
    echo "export OVH_CONSUMER_KEY='your-consumer-key'"
    echo "export OVH_PROJECT_NAME='your-project-name'"
    echo "export OVH_PROJECT_ID='your-project-id'"
    echo ""
    echo "For more information on obtaining OVH API credentials, see:"
    echo "https://docs.ovh.com/gb/en/api/first-steps-with-ovh-api/"
    exit 1
fi

echo "Environment variables configured:"
echo "  OVH_ENDPOINT: $OVH_ENDPOINT"
echo "  OVH_APPLICATION_KEY: ${OVH_APPLICATION_KEY:0:8}..."
echo "  OVH_APPLICATION_SECRET: ${OVH_APPLICATION_SECRET:0:8}..."
echo "  OVH_CONSUMER_KEY: ${OVH_CONSUMER_KEY:0:8}..."
echo "  OVH_PROJECT_NAME: $OVH_PROJECT_NAME"
echo "  OVH_PROJECT_ID: $OVH_PROJECT_ID"
echo ""

# Run the integration tests
echo "Running OVH provider integration tests..."
echo ""

# Change to the project root directory
cd "$(dirname "$0")/.."

# Run unit tests first
echo "Running unit tests..."
go test -v ./internal/providers/ovh/ -run "^Test[^I]"
echo ""

# Run integration tests
echo "Running integration tests..."
go test -v ./internal/providers/ovh/ -run "^TestIntegration"

echo ""
echo "Integration tests completed successfully!"
