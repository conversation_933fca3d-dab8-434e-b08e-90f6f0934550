package s3manager

import (
	"fmt"

	"github.com/spf13/cobra"
)

// ovhProjectsCmd represents the ovh-projects command
var ovhProjectsCmd = &cobra.Command{
	Use:   "ovh-projects",
	Short: "List OVH projects",
	Long:  "List OVH projects using the configured OVH API credentials.",
	RunE:  runOvhProjects,
}

func init() {
	rootCmd.AddCommand(ovhProjectsCmd)
}

func runOvhProjects(cmd *cobra.Command, args []string) error {
	// Access the global configuration
	config := GetGlobalConfig()

	// Check if OVH credentials are configured
	if config.OvhApplicationKey == "" {
		return fmt.Errorf("OVH application key not configured. Set OVH_APPLICATION_KEY environment variable or use --ovh-application-key flag")
	}

	if config.OvhApplicationSecret == "" {
		return fmt.Errorf("OVH application secret not configured. Set OVH_APPLICATION_SECRET environment variable or use --ovh-application-secret flag")
	}

	if config.OvhConsumerKey == "" {
		return fmt.Errorf("OVH consumer key not configured. Set OVH_CONSUMER_KEY environment variable or use --ovh-consumer-key flag")
	}

	fmt.Printf("Using OVH endpoint: %s\n", config.OvhEndpoint)
	fmt.Printf("Application Key: %s...\n", config.OvhApplicationKey[:min(8, len(config.OvhApplicationKey))])

	// Here you would implement the actual OVH API call
	fmt.Println("Listing OVH projects... (not implemented yet)")

	return nil
}
